#!/usr/bin/env python3
"""
Comprehensive API Testing Script for CTMS Move Listing APIs
Tests Trip, Shipment, and TransportOrder listing APIs with various filters
"""

import requests
import json
import time
import sys
from datetime import datetime, timedelta
import psycopg2
from psycopg2.extras import RealDictCursor

# Configuration
BASE_URL = "http://localhost:8093/move"
HEADERS = {
    "Content-Type": "application/json",
    "X-Tenant-Id": "CFR"
}

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'ctms_move_local',
    'user': 'postgres',
    'password': 'postgres'
}

class APITester:
    def __init__(self):
        self.test_results = []
        self.query_counts = {}
        
    def log_test(self, test_name, status, details=None):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        self.test_results.append(result)
        print(f"[{status}] {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def get_db_connection(self):
        """Get database connection"""
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            return conn
        except Exception as e:
            print(f"Database connection failed: {e}")
            return None
    
    def get_sample_data(self):
        """Get sample data from database for testing"""
        conn = self.get_db_connection()
        if not conn:
            return {}
            
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Get sample trip data
                cur.execute("""
                    SELECT id, code, status, transport_order_id, 
                           external_origin_location_code, external_destination_location_code
                    FROM "CFR".trip 
                    WHERE deleted_at IS NULL 
                    LIMIT 5
                """)
                trips = cur.fetchall()
                
                # Get sample shipment data
                cur.execute("""
                    SELECT id, code, status, trip_id, transport_order_id,
                           external_consignment_id, external_customer_order_id
                    FROM "CFR".shipment 
                    WHERE deleted_at IS NULL 
                    LIMIT 5
                """)
                shipments = cur.fetchall()
                
                # Get sample transport order data
                cur.execute("""
                    SELECT id, code, status
                    FROM "CFR".transport_order 
                    WHERE deleted_at IS NULL 
                    LIMIT 5
                """)
                transport_orders = cur.fetchall()
                
                # Get sample vehicle data
                cur.execute("""
                    SELECT id, code, external_resource_id, external_vehicle_type_id
                    FROM "CFR".vehicle_resource 
                    WHERE deleted_at IS NULL 
                    LIMIT 5
                """)
                vehicles = cur.fetchall()
                
                return {
                    'trips': [dict(row) for row in trips],
                    'shipments': [dict(row) for row in shipments],
                    'transport_orders': [dict(row) for row in transport_orders],
                    'vehicles': [dict(row) for row in vehicles]
                }
        except Exception as e:
            print(f"Error getting sample data: {e}")
            return {}
        finally:
            conn.close()
    
    def make_api_request(self, endpoint, payload):
        """Make API request and return response"""
        try:
            url = f"{BASE_URL}{endpoint}"
            response = requests.post(url, headers=HEADERS, json=payload, timeout=30)
            return {
                'status_code': response.status_code,
                'data': response.json() if response.content else None,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            return {
                'status_code': 0,
                'error': str(e),
                'response_time': 0
            }
    
    def test_trip_listing_basic(self):
        """Test basic trip listing without filters"""
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"}
        }
        
        response = self.make_api_request("/v1/trips/list", payload)
        
        if response['status_code'] == 200:
            data = response['data']
            self.log_test("Trip Listing - Basic", "PASS", {
                'total_records': data.get('totalRecords', 0),
                'returned_records': len(data.get('data', [])),
                'response_time': response['response_time']
            })
        else:
            self.log_test("Trip Listing - Basic", "FAIL", {
                'status_code': response['status_code'],
                'error': response.get('error', 'Unknown error')
            })
    
    def test_trip_listing_with_filters(self, sample_data):
        """Test trip listing with various filters"""
        if not sample_data.get('trips'):
            self.log_test("Trip Listing - Filters", "SKIP", "No sample trip data")
            return
            
        trip = sample_data['trips'][0]
        
        # Test with trip status filter
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "tripStatuses": [trip['status']]
            }
        }
        
        response = self.make_api_request("/v1/trips/list", payload)
        
        if response['status_code'] == 200:
            self.log_test("Trip Listing - Status Filter", "PASS", {
                'filter_status': trip['status'],
                'total_records': response['data'].get('totalRecords', 0),
                'response_time': response['response_time']
            })
        else:
            self.log_test("Trip Listing - Status Filter", "FAIL", {
                'status_code': response['status_code']
            })
    
    def test_shipment_listing_basic(self):
        """Test basic shipment listing without filters"""
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"}
        }
        
        response = self.make_api_request("/v1/shipments/list", payload)
        
        if response['status_code'] == 200:
            data = response['data']
            self.log_test("Shipment Listing - Basic", "PASS", {
                'total_records': data.get('totalRecords', 0),
                'returned_records': len(data.get('data', [])),
                'response_time': response['response_time']
            })
        else:
            self.log_test("Shipment Listing - Basic", "FAIL", {
                'status_code': response['status_code'],
                'error': response.get('error', 'Unknown error')
            })
    
    def test_transport_order_listing_basic(self):
        """Test basic transport order listing without filters"""
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"}
        }
        
        response = self.make_api_request("/v1/transport-orders/list", payload)
        
        if response['status_code'] == 200:
            data = response['data']
            self.log_test("TransportOrder Listing - Basic", "PASS", {
                'total_records': data.get('totalRecords', 0),
                'returned_records': len(data.get('data', [])),
                'response_time': response['response_time']
            })
        else:
            self.log_test("TransportOrder Listing - Basic", "FAIL", {
                'status_code': response['status_code'],
                'error': response.get('error', 'Unknown error')
            })
    
    def test_complex_filters(self, sample_data):
        """Test complex filter combinations"""
        if not sample_data.get('trips') or not sample_data.get('shipments'):
            self.log_test("Complex Filters Test", "SKIP", "Insufficient sample data")
            return

        trip = sample_data['trips'][0]
        shipment = sample_data['shipments'][0]

        # Test trip listing with multiple filters
        current_time = int(time.time() * 1000)
        one_day_ago = current_time - (24 * 60 * 60 * 1000)
        one_day_ahead = current_time + (24 * 60 * 60 * 1000)

        payload = {
            "pagination": {"pageNo": 0, "pageSize": 20},
            "sort": {"sortBy": "id", "sortDirection": "DESC"},
            "filter": {
                "tripStatuses": [trip['status']],
                "shipmentStatuses": [shipment['status']],
                "expectedStartDateRange": {
                    "from": one_day_ago,
                    "to": one_day_ahead
                }
            }
        }

        response = self.make_api_request("/v1/trips/list", payload)

        if response['status_code'] == 200:
            self.log_test("Trip Listing - Complex Filters", "PASS", {
                'total_records': response['data'].get('totalRecords', 0),
                'response_time': response['response_time']
            })
        else:
            self.log_test("Trip Listing - Complex Filters", "FAIL", {
                'status_code': response['status_code']
            })

    def test_extensive_filters(self, sample_data):
        """Test extensive filter combinations for all APIs"""
        print("\nTesting extensive filter combinations...")

        # Test Trip API with all possible filters
        self.test_trip_extensive_filters(sample_data)

        # Test Shipment API with all possible filters
        self.test_shipment_extensive_filters(sample_data)

        # Test TransportOrder API with all possible filters
        self.test_transport_order_extensive_filters(sample_data)

    def test_trip_extensive_filters(self, sample_data):
        """Test Trip API with extensive filter combinations"""
        if not sample_data.get('trips'):
            self.log_test("Trip Extensive Filters", "SKIP", "No sample trip data")
            return

        trip = sample_data['trips'][0]

        # Test 1: Multiple ID filters
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "tripIds": [str(trip['id'])],
                "transportOrderIds": [str(trip['transport_order_id'])] if trip['transport_order_id'] else []
            }
        }

        response = self.make_api_request("/v1/trips/list", payload)
        self.log_test("Trip - ID Filters",
                     "PASS" if response['status_code'] == 200 else "FAIL",
                     {'response_time': response['response_time'], 'status_code': response['status_code']})

        # Test 2: Location filters
        if trip['external_origin_location_code']:
            payload = {
                "pagination": {"pageNo": 0, "pageSize": 10},
                "sort": {"sortBy": "id", "sortDirection": "ASC"},
                "filter": {
                    "originLocationId": trip['external_origin_location_code']
                }
            }

            response = self.make_api_request("/v1/trips/list", payload)
            self.log_test("Trip - Origin Location Filter",
                         "PASS" if response['status_code'] == 200 else "FAIL",
                         {'response_time': response['response_time'], 'status_code': response['status_code']})

        # Test 3: Date range filters
        current_time = int(time.time() * 1000)
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "expectedStartDateRange": {
                    "from": current_time - (30 * 24 * 60 * 60 * 1000),  # 30 days ago
                    "to": current_time + (30 * 24 * 60 * 60 * 1000)     # 30 days ahead
                }
            }
        }

        response = self.make_api_request("/v1/trips/list", payload)
        self.log_test("Trip - Date Range Filter",
                     "PASS" if response['status_code'] == 200 else "FAIL",
                     {'response_time': response['response_time'], 'status_code': response['status_code']})

    def test_shipment_extensive_filters(self, sample_data):
        """Test Shipment API with extensive filter combinations"""
        if not sample_data.get('shipments'):
            self.log_test("Shipment Extensive Filters", "SKIP", "No sample shipment data")
            return

        shipment = sample_data['shipments'][0]

        # Test 1: Multiple ID filters
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "shipmentIds": [str(shipment['id'])],
                "tripIds": [str(shipment['trip_id'])] if shipment['trip_id'] else [],
                "consignmentIds": [shipment['external_consignment_id']] if shipment['external_consignment_id'] else []
            }
        }

        response = self.make_api_request("/v1/shipments/list", payload)
        self.log_test("Shipment - ID Filters",
                     "PASS" if response['status_code'] == 200 else "FAIL",
                     {'response_time': response['response_time'], 'status_code': response['status_code']})

        # Test 2: Status filters
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "shipmentStatuses": [shipment['status']]
            }
        }

        response = self.make_api_request("/v1/shipments/list", payload)
        self.log_test("Shipment - Status Filter",
                     "PASS" if response['status_code'] == 200 else "FAIL",
                     {'response_time': response['response_time'], 'status_code': response['status_code']})

    def test_transport_order_extensive_filters(self, sample_data):
        """Test TransportOrder API with extensive filter combinations"""
        if not sample_data.get('transport_orders'):
            self.log_test("TransportOrder Extensive Filters", "SKIP", "No sample transport order data")
            return

        to = sample_data['transport_orders'][0]

        # Test 1: ID and status filters
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 10},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "transportOrderIds": [str(to['id'])],
                "transportOrderStatuses": [to['status']]
            }
        }

        response = self.make_api_request("/v1/transport-orders/list", payload)
        self.log_test("TransportOrder - ID & Status Filters",
                     "PASS" if response['status_code'] == 200 else "FAIL",
                     {'response_time': response['response_time'], 'status_code': response['status_code']})

    def verify_database_consistency(self, sample_data):
        """Verify API responses match database data"""
        print("\nVerifying database consistency...")

        if not sample_data.get('trips'):
            self.log_test("Database Consistency", "SKIP", "No sample data")
            return

        trip = sample_data['trips'][0]

        # Get trip from API
        payload = {
            "pagination": {"pageNo": 0, "pageSize": 1},
            "sort": {"sortBy": "id", "sortDirection": "ASC"},
            "filter": {
                "tripIds": [str(trip['id'])]
            }
        }

        api_response = self.make_api_request("/v1/trips/list", payload)

        if api_response['status_code'] != 200 or not api_response['data']['data']:
            self.log_test("Database Consistency - Trip", "FAIL", "API call failed or no data")
            return

        api_trip = api_response['data']['data'][0]

        # Compare with database
        conn = self.get_db_connection()
        if not conn:
            self.log_test("Database Consistency - Trip", "FAIL", "Database connection failed")
            return

        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT id, code, status
                    FROM "CFR".trip
                    WHERE id = %s AND deleted_at IS NULL
                """, (trip['id'],))
                db_trip = cur.fetchone()

                if db_trip:
                    # Compare key fields
                    matches = (
                        str(api_trip['id']) == str(db_trip['id']) and
                        api_trip['code'] == db_trip['code'] and
                        api_trip['status'] == db_trip['status']
                    )

                    self.log_test("Database Consistency - Trip",
                                 "PASS" if matches else "FAIL",
                                 {
                                     'api_id': api_trip['id'],
                                     'db_id': db_trip['id'],
                                     'matches': matches
                                 })
                else:
                    self.log_test("Database Consistency - Trip", "FAIL", "Trip not found in database")

        except Exception as e:
            self.log_test("Database Consistency - Trip", "FAIL", f"Database error: {e}")
        finally:
            conn.close()
    
    def run_all_tests(self):
        """Run all API tests"""
        print("Starting comprehensive API testing...")
        print("=" * 50)

        # Get sample data
        sample_data = self.get_sample_data()
        print(f"Retrieved sample data: {len(sample_data.get('trips', []))} trips, "
              f"{len(sample_data.get('shipments', []))} shipments, "
              f"{len(sample_data.get('transport_orders', []))} transport orders")

        # Run basic tests
        self.test_trip_listing_basic()
        self.test_shipment_listing_basic()
        self.test_transport_order_listing_basic()

        # Run filter tests
        self.test_trip_listing_with_filters(sample_data)
        self.test_complex_filters(sample_data)

        # Run extensive filter tests
        self.test_extensive_filters(sample_data)

        # Verify database consistency
        self.verify_database_consistency(sample_data)

        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        passed = len([t for t in self.test_results if t['status'] == 'PASS'])
        failed = len([t for t in self.test_results if t['status'] == 'FAIL'])
        skipped = len([t for t in self.test_results if t['status'] == 'SKIP'])
        
        print(f"Total Tests: {len(self.test_results)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Skipped: {skipped}")
        
        if failed > 0:
            print("\nFAILED TESTS:")
            for test in self.test_results:
                if test['status'] == 'FAIL':
                    print(f"  - {test['test_name']}: {test['details']}")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
