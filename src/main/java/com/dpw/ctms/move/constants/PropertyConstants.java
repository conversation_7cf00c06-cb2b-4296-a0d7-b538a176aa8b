package com.dpw.ctms.move.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PropertyConstants {
    public static final String PROP_TRIP_SHIPMENT_STATUS_IN_GET_API = "TRIP_SHIPMENT_STATUS_IN_GET_API";
    public static final String PROP_MAX_LIMIT_PAGE_SIZE = "PROP_MAX_LIMIT_PAGE_SIZE";
    public static final String STOP_CODE_PREFIX = "STOP";
    public static final String FILE_TYPE_PDF = "pdf";
    public static final String PRESIGNED_DOWNLOAD_LINK = "presignedDownloadLink";
    public static final int LINK_EXPIRY_DURATION = 60;
    public static final String DEFAULT_TIME_ZONE_ID = "UTC";
    //TODO: Move to config based on BU
    public static final String DATE_FORMAT = "yyyy/MM/dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm";
}