package com.dpw.ctms.move.constants;

import com.dpw.ctms.move.enums.TaskRole;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RoleType {

    public static final List<String> VEHICLE_OPERATION_ROLE_TYPE = List.of(
            TaskRole.READ.name(),
            TaskRole.UPDATE.name()
    );
    public static final String CONTROLLER = "CONTROLLER";

}
