package com.dpw.ctms.move.dto.consumer.cfr;

import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.util.JSONDeserializer.TimeDTODeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@Data
@SuperBuilder
public class DeliveryTaskMessageDTO extends IntegratorTaskMessageRequestDTO<
        DeliveryTaskMessageDTO.PercolatedRecordDTO> {

    @AllArgsConstructor
    @Data
    @SuperBuilder
    @NoArgsConstructor
    public static class PercolatedRecordDTO {

        @JsonDeserialize(using = TimeDTODeserializer.class)
        private TimeDTO arrivalTime;

        @JsonDeserialize(using = TimeDTODeserializer.class)
        private TimeDTO unloadingCompletionTime;

        @JsonDeserialize(using = TimeDTODeserializer.class)
        private TimeDTO departureTime;

        private String bolIdentifier;
        private String bolUploadTime;
    }
}