package com.dpw.ctms.move.dto.document;

import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryTaskDocumentDTO {
    private String asyncMappingUUID;
    private String entityId;
    private String entityType;
    private DocumentOperationType operationType;
    private DocumentType documentType;
}