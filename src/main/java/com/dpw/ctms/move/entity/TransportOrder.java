package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.envers.Audited;
import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "transport_order")
@EqualsAndHashCode(callSuper = true)
@Audited
public class TransportOrder extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private TransportOrderStatus status;

    @Column(name = "assignment_type")
    @Enumerated(EnumType.STRING)
    private AssignmentType assignmentType;

    @Column(name = "assignment_code")
    private String assignmentCode;

    @Column(name = "assignee_identifier")
    private String assigneeIdentifier;

    @Column(name = "deleted_at")
    private Long deletedAt;

    @OneToMany(mappedBy = "transportOrder", cascade = CascadeType.ALL)
    @JsonManagedReference("transport-order-trip")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Set<Trip> trips;

    @OneToMany(mappedBy = "transportOrder", cascade = CascadeType.ALL)
    @JsonManagedReference("transport-order-shipment")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Set<Shipment> shipments;
}
