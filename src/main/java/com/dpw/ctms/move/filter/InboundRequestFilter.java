package com.dpw.ctms.move.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

import static com.dpw.ctms.move.constants.MoveConstants.*;

@Component
@Order(1)
public class InboundRequestFilter implements Filter {

    private static final String TRACE_ID_HEADER = "X-Trace-Id";
    private static final String TRANSACTION_ID_HEADER = "X-Transaction-Id";
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String COMPANY_REFERENCE_HEADER = "X-Company-Reference";
    private static final String CURRENT_LOCATION_HEADER = "X-Current-Location";
    private static final String CURRENT_CHANNEL_TYPE_HEADER = "X-Current-Channel-Type";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String traceId = getOrGenerate(httpRequest.getHeader(TRACE_ID_HEADER));
        String transactionId = getOrGenerate(httpRequest.getHeader(TRANSACTION_ID_HEADER));
        String userId = httpRequest.getHeader(USER_ID_HEADER);
        String companyReference = httpRequest.getHeader(COMPANY_REFERENCE_HEADER);
        String currentLocation = httpRequest.getHeader(CURRENT_LOCATION_HEADER);
        String currentChannelType = httpRequest.getHeader(CURRENT_CHANNEL_TYPE_HEADER);

        MDC.put(TRACE_ID, traceId);
        MDC.put(TRANSACTION_ID, transactionId);
        putIfNotNull(USER_ID, userId);
        putIfNotNull(COMPANY_REFERENCE, companyReference);
        putIfNotNull(CURRENT_LOCATION, currentLocation);
        putIfNotNull(CURRENT_CHANNEL_TYPE, currentChannelType);
        try {
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }

    private String getOrGenerate(String incomingId) {
        return (incomingId != null && !incomingId.isBlank())
                ? incomingId
                : UUID.randomUUID().toString();
    }

    private void putIfNotNull(String key, String value) {
        if (value != null) {
            MDC.put(key, value);
        }
    }
}

