package com.dpw.ctms.move.integration.mapper.oms;

import com.dpw.ctms.move.dto.DateTimeDTO;
import com.dpw.ctms.move.integration.response.LabelValue;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.dto.ConsignmentDetailsDTO;
import com.dpw.ctms.move.dto.CustomerOrderMetaDataDTO;
import com.dpw.ctms.move.dto.ProductDetailsDTO;
import com.dpw.ctms.move.util.DateTimeUtil;
import com.dpw.ctms.move.constants.ProductPropertyConstants;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OmsConsignmentMapper {

    @Autowired
    protected DateTimeUtil dateTimeUtil;

    @Mapping(target = "consignmentId", expression = "java(String.valueOf(consignmentRecord.getId()))")
    @Mapping(target = "consignmentCode", source = "code")
    @Mapping(target = "lineItemId", source = "lineItemId")
    @Mapping(target = "lineItemCode", source = "lineItemCode")
    @Mapping(target = "customerOrderId", source = "customerOrderId")
    @Mapping(target = "customerOrderCode", source = "customerOrderCode")
    @Mapping(target = "specialInstructions", source = "lineItemMetadata.specialInstructions", defaultValue = "")
    @Mapping(target = "customerId", source = "customer.id")
    @Mapping(target = "originFacilityId", source = "origin.id")
    @Mapping(target = "destinationFacilityId", source = "destination.id")
    @Mapping(target = "expectedPickupTime", source = "expectedPickupTime", qualifiedByName = "mapTimeInfo")
    @Mapping(target = "expectedDeliveryTime", source = "expectedDeliveryTime", qualifiedByName = "mapTimeInfo")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapLabelValue")
    @Mapping(target = "movementType", source = "movementType", qualifiedByName = "mapLabelValue")
    @Mapping(target = "productDetailsDTO", source = "products", qualifiedByName = "mapProductDetails")
    @Mapping(target = "shipmentIds", expression = "java(new java.util.ArrayList<>())")
    @Mapping(target = "customerOrderMetaData", source = "customerOrderMetadata")
    public abstract ConsignmentDetailsDTO mapToConsignmentDetails(ConsignmentRecord consignmentRecord);

    @Named("mapTimeInfo")
    protected DateTimeDTO mapTimeInfo(ConsignmentRecord.TimeInfo timeInfo) {
        if (timeInfo == null || timeInfo.getEpoch() == null) {
            return null;
        }
        return dateTimeUtil.fromEpochMillis(timeInfo.getEpoch(), timeInfo.getTimeZoneCode());
    }

    @Named("mapLabelValue")
    public String mapLabelValue(LabelValue labelValue) {
        return labelValue != null ? labelValue.getValue() : null;
    }

    @Named("mapProductDetails")
    public ProductDetailsDTO mapProductDetails(List<ConsignmentRecord.Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        return products.stream()
                .findFirst()
                .map(this::mapSingleProduct)
                .orElse(null);
    }

    public ProductDetailsDTO mapSingleProduct(ConsignmentRecord.Product product) {
        if (product == null) {
            return null;
        }

        var builder = ProductDetailsDTO.builder()
                .id(product.getId())
                .resourceId(Optional.ofNullable(product.getResourceDetails())
                        .map(ConsignmentRecord.Product.ResourceDetails::getId)
                        .orElse(null));

        if (!CollectionUtils.isEmpty(product.getProperties())) {
            Map<String, ProductDetailsDTO.PropertyDetails> propertiesMap = product.getProperties().stream()
                    .map(property -> ProductDetailsDTO.PropertyDetails.builder()
                            .name(Optional.ofNullable(property.getPropertyName())
                                    .map(LabelValue::getValue)
                                    .orElse(null))
                            .value(property.getPropertyValue())
                            .resourceUomId(Optional.ofNullable(property.getUnitOfMeasurement())
                                    .map(ConsignmentRecord.Product.Property.UnitOfMeasurement::getId)
                                    .orElse(null))
                            .build())
                    .collect(Collectors.toMap(ProductDetailsDTO.PropertyDetails::getName, p -> p));
            
            // Calculate volume if not present but dimensions are available
            calculateAndAddVolumeIfMissing(propertiesMap);
            
            builder.properties(propertiesMap);
        }

        return builder.build();
    }

    private void calculateAndAddVolumeIfMissing(Map<String, ProductDetailsDTO.PropertyDetails> propertiesMap) {
        if (propertiesMap == null) {
            return;
        }

        // Check if VOLUME already exists
        if (propertiesMap.containsKey(ProductPropertyConstants.VOLUME)) {
            return;
        }

        // Check if all dimensions are present
        ProductDetailsDTO.PropertyDetails lengthProperty = propertiesMap.get(ProductPropertyConstants.LENGTH);
        ProductDetailsDTO.PropertyDetails breadthProperty = propertiesMap.get(ProductPropertyConstants.BREADTH);
        ProductDetailsDTO.PropertyDetails heightProperty = propertiesMap.get(ProductPropertyConstants.HEIGHT);

        if (lengthProperty != null && lengthProperty.getValue() != null &&
            breadthProperty != null && breadthProperty.getValue() != null &&
            heightProperty != null && heightProperty.getValue() != null) {
            
            // Calculate volume
            double volume = lengthProperty.getValue() * breadthProperty.getValue() * heightProperty.getValue();
            
            // Add volume property with the same UOM as length
            ProductDetailsDTO.PropertyDetails volumeProperty = ProductDetailsDTO.PropertyDetails.builder()
                    .name(ProductPropertyConstants.VOLUME)
                    .value(volume)
                    .resourceUomId(lengthProperty.getResourceUomId())
                    .build();
            
            propertiesMap.put(ProductPropertyConstants.VOLUME, volumeProperty);
        }
    }
}