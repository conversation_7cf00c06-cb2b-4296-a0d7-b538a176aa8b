package com.dpw.ctms.move.kafka.consumer;

import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.tmsutils.annotation.OverrideKafkaTenantStrategy;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.threadlocal.TenantContext;
import com.dpw.ctms.move.dto.consumer.IntegratorMessageRequestDTO;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class ShipmentTaskConsumer {

    @Value("#{${task-code-mapping}}")
    private Map<String, String> taskCodeMap;

    private final TaskActionHandlerRegistry taskActionHandlerRegistry;
    @KafkaListener(
            groupId = "${kafka.consumer.shipment-task-acknowledgement.consumer-group-id:dpw-task-execution-results-dev}",
            topics = "${kafka.consumer.shipment-task-acknowledgement.topic:dpw-task-execution-results-dev}",
            containerFactory = "${kafka.consumer.shipment-task-acknowledgement.container-factory:shipmentTaskContainerFactory}",
            autoStartup = "${kafka.consumer.shipment-task-acknowledgement.enable:false}")
    public void handlePayload(ConsumerRecord<String, String> record) {
        try {
            String jsonMessage = record.value();
            log.debug("Received message from topic: {}, partition: {}, offset: {}, message: {}",
                    record.topic(), record.partition(), record.offset(), jsonMessage);
            IntegratorTaskMessageRequestDTO message = ObjectMapperUtil.getObjectMapper().readValue(jsonMessage,
                    new TypeReference<IntegratorTaskMessageRequestDTO>() {
                    });
            log.info("Received the Event from task service having event: {}", message);
            if (message == null) return;
            String taskCode = message.getMessage().getTaskDetails().getTaskMasterCode();
            String taskType = taskCodeMap.get(taskCode);
            if (taskType == null) {
                log.error("No task type found for task code: {}", taskCode);
                throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                        String.format("Invalid task code: %s",taskCode));
            }
            TaskActionHandler handler = taskActionHandlerRegistry.getHandler(taskType);
            log.info("Dispatching action: {} to handler: {}", taskType, handler.getClass().getName());
            handler.handle(message);
        } catch (Exception e) {
            log.error("Error while processing message from topic: {}, partition: {}, offset: {}, message: {}",
                    record.topic(), record.partition(), record.offset(), record.value(), e);
        }
    }
}