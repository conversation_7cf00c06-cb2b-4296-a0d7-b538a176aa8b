package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.response.EntityDocumentResponse;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentOperationType;
import org.mapstruct.*;

/**
 * Maps {@link Document} ↔ {@link DocumentDTO}.
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DocumentMapper {

    @Mappings({
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum",     source = "checksum"),
            @Mapping(target = "fileIdentifier",  source = "fileIdentifier"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "documentOperationType", source = "documentOperationType"),
    })
    DocumentDTO toDTO(Document document);

    @Mappings({
            @Mapping(target = "documentType", source = "documentType"),
            @Mapping(target = "checksum", source = "checksum"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "status", source = "status"),
            @Mapping(target = "documentOperationType", source = "documentOperationType"),
    })
    Document toEntity(DocumentDTO dto);

    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
    })
    Document fromPreSignedUrlEvent(PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
    })
    void updateDocumentFromPreSignedUrlEvent(@MappingTarget Document document, PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "documentOperationType", source = "operationType"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
    })
    Document fromDeliveryTaskDocumentDTO(DeliveryTaskDocumentDTO dto);

    @Mappings({
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
    })
    void updateDocumentFromDeliveryTaskDTO(@MappingTarget Document document, DeliveryTaskDocumentDTO dto);

    // Overloaded updateDocument methods with direct mappings
    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
    })
    void updateDocument(@MappingTarget Document document, PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "documentOperationType", source = "operationType"),
            @Mapping(target = "documentType", source = "documentType"),
    })
    void updateDocument(@MappingTarget Document document, DeliveryTaskDocumentDTO dto);

    // Overloaded createDocument methods with direct mappings
    @Mappings({
            @Mapping(target = "clientIdentifier", source = "clientIdentifier"),
            @Mapping(target = "asyncMappingUUID", source = "fileKey"),
            @Mapping(target = "fileIdentifier", source = "fileIdentifier"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "presignedDownloadUrl", source = "presignedDownloadUrl"),
            @Mapping(target = "fileMetadata", source = "fileMetadata"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
            @Mapping(target = "documentOperationType", expression = "java(com.dpw.ctms.move.enums.DocumentOperationType.UPLOAD)"),
    })
    Document createDocument(PreSignedUrlEvent event);

    @Mappings({
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "entityId", source = "entityId"),
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "documentOperationType", source = "operationType"),
            @Mapping(target = "status", expression = "java(com.dpw.ctms.move.enums.DocumentStatus.INACTIVE)"),
            @Mapping(target = "documentType", source = "documentType"),
    })
    Document createDocument(DeliveryTaskDocumentDTO dto);

    @Mappings({
            @Mapping(target = "entityType", source = "entityType"),
            @Mapping(target = "entityCode", source = "entityId"),
            @Mapping(target = "asyncMappingUUID", source = "asyncMappingUUID"),
            @Mapping(target = "fileName", source = "fileName"),
            @Mapping(target = "fileSize", source = "fileSize"),
            @Mapping(target = "fileType", source = "fileType"),
            @Mapping(target = "externalDocumentIdentifier", source = "fileIdentifier")
    })
    EntityDocumentResponse.FileDetail toFileDetail(Document document);

}
