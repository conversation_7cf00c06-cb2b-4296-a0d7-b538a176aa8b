package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.TransportOrderDetailsResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
//TODO: Seperate Mapper for details
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        uses = {TripMapper.class, StopMapper.class, TaskMapper.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TransportOrderMapper {
    TransportOrderDTO toDTO(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest);

    TransportOrder toEntity(TransportOrderDTO transportOrderDTO);

    @AfterMapping
    default void setBackReference(@MappingTarget TransportOrder transportOrder) {
        Set<Shipment> mergedShipments = new HashSet<>();
        if (transportOrder.getTrips() != null) {
            for (Trip trip : transportOrder.getTrips()) {
                trip.setTransportOrder(transportOrder);
                if (trip.getShipments() != null) {
                    for (Shipment shipment: trip.getShipments()) {
                        shipment.setTransportOrder(transportOrder);
                        mergedShipments.add(shipment);
                    }
                }
            }
        }
        if (transportOrder.getShipments() != null) {
            for (Shipment shipment : transportOrder.getShipments()) {
                shipment.setTransportOrder(transportOrder);
                mergedShipments.add(shipment);
            }
        }
        transportOrder.setShipments(mergedShipments);
    }

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapTransportOrderEntityToResponseStatusInfo")
    @Mapping(target = "customerOrders", source = "shipments", qualifiedByName = "mapCustomerOrders")
    @Mapping(target = "assignmentDetails", source = ".", qualifiedByName = "mapAssignmentDetails")
    @Mapping(target = "trips",source = "trips", qualifiedByName = "mapTrips")
    @Mapping(target = "updatedAt", source = "updatedAt")
    TransportOrderListingResponse mapToResponse(TransportOrder transportOrder);

    @Named("mapTransportOrderEntityToResponseStatusInfo")
    default EnumLabelValueResponse mapStatusInfo(DisplayableStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return new EnumLabelValueResponse(statusEnum.getDisplayName(), statusEnum.name());
    }

    @Named("mapCustomerOrders")
    default List<TransportOrderListingResponse.CustomerOrder> mapCustomerOrders(Set<Shipment> shipments) {
        if (CollectionUtils.isEmpty(shipments)) {
            return new ArrayList<>();
        }

        return shipments.stream()
                .filter(shipment -> StringUtils.hasText(shipment.getExternalCustomerOrderId()))
                .map(shipment -> TransportOrderListingResponse.CustomerOrder.builder()
                        .id(shipment.getExternalCustomerOrderId())
                        .updatedAt(shipment.getUpdatedAt())
                        .build())
                .distinct()
                .collect(Collectors.toList());
    }

    @Named("mapAssignmentDetails")
    default TransportOrderListingResponse.AssignmentDetails mapAssignmentDetails(TransportOrder transportOrder) {
        TransportOrderListingResponse.AssignmentDetails.AssignmentDetailsBuilder builder =
                TransportOrderListingResponse.AssignmentDetails.builder();

        if (transportOrder.getAssignmentType() != null) {
            builder.type(mapStatusInfo(transportOrder.getAssignmentType()));
        }

        if (transportOrder.getAssigneeIdentifier() != null) {
            builder.vendor(
                    TransportOrderListingResponse.Vendor.builder()
                            .code(transportOrder.getAssigneeIdentifier())
                            .build()
            );
        }

        return builder.build();
    }

    @Named("mapTrips")
    default List<TransportOrderListingResponse.Trip> mapTrips(Set<Trip> trips) {
        if (CollectionUtils.isEmpty(trips)) {
            return new ArrayList<>();
        }

        return trips.stream()
                .map(trip -> TransportOrderListingResponse.Trip.builder()
                        .code(trip.getCode())
                        .updatedAt(trip.getUpdatedAt())
                        .build())
                .distinct()
                .collect(Collectors.toList());
    }

    @Mapping(target = "createdBy", source = "createdBy")
    @Mapping(target = "createdAt", source = "createdAt")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapTransportOrderEntityToResponseStatusInfo")
    @Mapping(target = "trips", source = "trips", qualifiedByName = "mapTripsToTripDetails")
    @Mapping(target = "assignmentDetails.assignmentType", source = "assignmentType", qualifiedByName = "mapTransportOrderEntityToResponseStatusInfo")
    @Mapping(target = "assignmentDetails.assigneeIdentifier", source = "assigneeIdentifier")
    TransportOrderDetailsResponse mapToDetailsResponse(TransportOrder transportOrder);

    @Named("mapTripsToTripDetails")
    default List<TransportOrderDetailsResponse.TripDetails> mapTripsToTripDetails(Set<Trip> trips) {
        if (CollectionUtils.isEmpty(trips)) {
            return new ArrayList<>();
        }

        return trips.stream()
                .map(trip -> TransportOrderDetailsResponse.TripDetails.builder()
                        .tripCode(trip.getCode())
                        .build())
                .collect(Collectors.toList());
    }
}
