package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Shipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.dpw.ctms.move.specification.ShipmentSpecifications.codeIn;
import static com.dpw.ctms.move.specification.ShipmentSpecifications.hasCode;
import static com.dpw.ctms.move.specification.ShipmentSpecifications.notDeleted;

@Repository
public interface ShipmentRepository extends JpaRepository<Shipment, Long>, JpaSpecificationExecutor<Shipment>, ShipmentRepositoryCustom {

    default Optional<Shipment> findByCodeAndDeletedAtIsNull(String code) {
        return findOne(hasCode(code).and(notDeleted()));
    }

    default List<Shipment> findAllByCodeInAndDeletedAtIsNull(Set<String> codes) {
        return findAll(codeIn(codes).and(notDeleted()));
    }
}