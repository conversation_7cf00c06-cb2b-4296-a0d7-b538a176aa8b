package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.repository.common.BaseOptimizedListingRepositoryImpl;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Repository;

/**
 * Custom repository implementation for optimized Shipment queries
 */
@Repository
public class ShipmentRepositoryCustomImpl extends BaseOptimizedListingRepositoryImpl<Shipment> implements ShipmentRepositoryCustom {



    @Override
    protected Class<Shipment> getEntityClass() {
        return Shipment.class;
    }

    @Override
    protected void configureEagerFetching(Root<Shipment> root) {
        Fetch<?, ?> tripFetch = root.fetch("trip", JoinType.LEFT);
        root.fetch("transportOrder", JoinType.LEFT);
        root.fetch("originStop", JoinType.LEFT);
        root.fetch("destinationStop", JoinType.LEFT);

        tripFetch.fetch("vehicleResource", JoinType.LEFT);
    }

    @Override
    protected Long getEntityId(Shipment entity) {
        return entity.getId();
    }
}
