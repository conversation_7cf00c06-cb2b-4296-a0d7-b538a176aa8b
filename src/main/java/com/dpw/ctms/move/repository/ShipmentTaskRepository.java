package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.ShipmentTask;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

import static com.dpw.ctms.move.specification.ShipmentTaskSpecifications.hasTaskId;
import static com.dpw.ctms.move.specification.ShipmentTaskSpecifications.notDeleted;

@Repository
public interface ShipmentTaskRepository extends JpaRepository<ShipmentTask, Long>, JpaSpecificationExecutor<ShipmentTask> {
    @EntityGraph(attributePaths = {"task"})
    List<ShipmentTask> findAllByShipmentIdIn(Set<Long> shipmentIds);

    default List<ShipmentTask> findAllByTaskIdAndDeletedAtIsNull(Long taskId) {
        return findAll(hasTaskId(taskId).and(notDeleted()));
    }
}
