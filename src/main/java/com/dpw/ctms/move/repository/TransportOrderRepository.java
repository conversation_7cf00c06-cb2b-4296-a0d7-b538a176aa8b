package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.dpw.ctms.move.specification.TransportOrderSpecifications.fetchTripsAndShipments;
import static com.dpw.ctms.move.specification.TransportOrderSpecifications.hasCode;
import static com.dpw.ctms.move.specification.TransportOrderSpecifications.notDeleted;

@Repository
public interface TransportOrderRepository extends JpaRepository<TransportOrder, Long>, JpaSpecificationExecutor<TransportOrder>, TransportOrderRepositoryCustom {
    default Optional<TransportOrder> findByCodeAndDeletedAtIsNull(String code) {
        return findOne(hasCode(code).and(notDeleted()).and(fetchTripsAndShipments()));
    }
}
