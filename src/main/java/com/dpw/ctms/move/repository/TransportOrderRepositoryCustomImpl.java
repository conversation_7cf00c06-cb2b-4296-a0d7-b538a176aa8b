package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.repository.common.BaseOptimizedListingRepositoryImpl;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Repository;

/**
 * Custom repository implementation for optimized TransportOrder queries
 */
@Repository
public class TransportOrderRepositoryCustomImpl extends BaseOptimizedListingRepositoryImpl<TransportOrder> implements TransportOrderRepositoryCustom {

    @Override
    protected Class<TransportOrder> getEntityClass() {
        return TransportOrder.class;
    }

    @Override
    protected void configureEagerFetching(Root<TransportOrder> root) {
        Fetch<?, ?> tripsFetch = root.fetch("trips", JoinType.LEFT);
        root.fetch("shipments", JoinType.LEFT);

        tripsFetch.fetch("vehicleResource", JoinType.LEFT);
    }

    @Override
    protected Long getEntityId(TransportOrder entity) {
        return entity.getId();
    }
}
