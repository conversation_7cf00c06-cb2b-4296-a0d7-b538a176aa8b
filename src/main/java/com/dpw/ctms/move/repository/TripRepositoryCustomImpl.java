package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.repository.common.BaseOptimizedListingRepositoryImpl;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import org.springframework.stereotype.Repository;

/**
 * Custom repository implementation for optimized Trip queries
 */
@Repository
public class TripRepositoryCustomImpl extends BaseOptimizedListingRepositoryImpl<Trip> implements TripRepositoryCustom {

    @Override
    protected Class<Trip> getEntityClass() {
        return Trip.class;
    }

    @Override
    protected void configureEagerFetching(Root<Trip> root) {
        root.fetch("transportOrder", JoinType.LEFT);
        root.fetch("shipments", JoinType.LEFT);
        root.fetch("vehicleResource", JoinType.LEFT);
        root.fetch("trailerResources", JoinType.LEFT);
        root.fetch("vehicleOperatorResources", JoinType.LEFT);
    }

    @Override
    protected Long getEntityId(Trip entity) {
        return entity.getId();
    }


}
