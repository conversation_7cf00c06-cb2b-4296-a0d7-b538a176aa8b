package com.dpw.ctms.move.repository.common;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.From;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Context class to track JOINs and enable sharing between filtering and sorting.
 * Handles both simple and nested join paths (e.g., "trip", "trip.vehicleResource", "trip.vehicleResource.code").
 */
class QueryJoinContext {
    private final Map<String, Join<?, ?>> joins = new HashMap<>();
    private final Root<?> root;

    public QueryJoinContext(Root<?> root) {
        this.root = root;
    }

    /**
     * Gets or creates a JOIN for the given path, handling nested relationships.
     * Examples:
     * - "trip" -> root.join("trip")
     * - "trip.vehicleResource" -> root.join("trip").join("vehicleResource")
     */
    @SuppressWarnings("unchecked")
    public <X, Y> Join<X, Y> getOrCreateJoin(String joinPath, JoinType joinType) {
        Join<?, ?> existingJoin = joins.get(joinPath);
        if (existingJoin != null) {
            return (Join<X, Y>) existingJoin;
        }

        String[] pathParts = joinPath.split("\\.");
        From<?, ?> currentFrom = root;
        StringBuilder currentPath = new StringBuilder();
        Join<?, ?> finalJoin = null;

        // Build the join path step by step
        for (String part : pathParts) {
            if (currentPath.length() > 0) {
                currentPath.append(".");
            }
            currentPath.append(part);

            String currentPathStr = currentPath.toString();

            // Check if we already have this intermediate join
            Join<?, ?> intermediateJoin = joins.get(currentPathStr);
            if (intermediateJoin != null) {
                currentFrom = intermediateJoin;
                finalJoin = intermediateJoin;
            } else {
                // Create new join and store it
                Join<?, ?> newJoin = currentFrom.join(part, joinType);
                joins.put(currentPathStr, newJoin);
                currentFrom = newJoin;
                finalJoin = newJoin;
            }
        }

        return (Join<X, Y>) finalJoin;
    }

    /**
     * Manually adds a JOIN to the context. This is used by Specifications to register
     * their JOINs so they can be reused by sorting logic.
     */
    public void addJoin(String joinPath, Join<?, ?> join) {
        joins.put(joinPath, join);
    }

    public Map<String, Join<?, ?>> getAllJoins() {
        return new HashMap<>(joins);
    }
}

/**
 * Base implementation for optimized listing operations with shared JOIN support
 */
@Slf4j
public abstract class BaseOptimizedListingRepositoryImpl<T> implements OptimizedListingRepository<T> {

    @PersistenceContext
    protected EntityManager entityManager;

    protected abstract Class<T> getEntityClass();
    protected abstract void configureEagerFetching(Root<T> root);

    /**
     * Get the primary key field name for this entity
     */
    protected String getPrimaryKeyFieldName() {
        return "id";
    }

    /**
     * Get the soft delete field name for this entity
     */
    protected String getSoftDeleteFieldName() {
        return "deletedAt";
    }

    @Override
    public Page<T> findAllOptimized(Specification<T> specification, Pageable pageable) {
        List<Long> entityIds = getEntityIdsWithPagination(specification, pageable);

        if (entityIds.isEmpty()) {
            log.info("No {} IDs found, returning empty page", getEntityClass().getSimpleName());
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        
        long totalCount = getTotalCount(specification);
        
        List<T> entities = findEntitiesWithRelatedDataByIds(entityIds);
        
        Map<Long, T> entityMap = entities.stream()
                .collect(Collectors.toMap(this::getEntityId, entity -> entity));
        
        List<T> orderedEntities = entityIds.stream()
                .map(entityMap::get)
                .collect(Collectors.toList());
        
        log.info("Successfully loaded {} {} with eager relationships", orderedEntities.size(), getEntityClass().getSimpleName());
        return new PageImpl<>(orderedEntities, pageable, totalCount);
    }

    @Override
    public List<T> findEntitiesWithRelatedDataByIds(List<Long> entityIds) {
        if (entityIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        log.info("Eagerly loading {} {} with related data", entityIds.size(), getEntityClass().getSimpleName());

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<T> query = cb.createQuery(getEntityClass());
        Root<T> root = query.from(getEntityClass());
        
        configureEagerFetching(root);

        Predicate idPredicate = root.get(getPrimaryKeyFieldName()).in(entityIds);
        Predicate notDeletedPredicate = cb.isNull(root.get(getSoftDeleteFieldName()));

        query.select(root)
             .where(cb.and(idPredicate, notDeletedPredicate))
             .distinct(true);

        return entityManager.createQuery(query).getResultList();
    }

    protected abstract Long getEntityId(T entity);

    private List<Long> getEntityIdsWithPagination(Specification<T> specification, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> query = cb.createQuery(Long.class);
        Root<T> root = query.from(getEntityClass());

        // Create join context to track and share JOINs
        QueryJoinContext joinContext = new QueryJoinContext(root);

        if (specification != null) {
            Predicate predicate = specification.toPredicate(root, query, cb);
            if (predicate != null) {
                query.where(predicate);
            }

            // After specification execution, scan for existing joins and register them in the context
            registerExistingJoins(root, joinContext);
        }

        query.select(root.get(getPrimaryKeyFieldName()));

        // Apply sorting - this will reuse JOINs from the join context if they exist
        if (pageable.getSort().isSorted()) {
            List<Order> orders = new ArrayList<>();
            pageable.getSort().forEach(sortOrder -> {
                Path<Object> path = getNestedPathWithSharedJoins(root, sortOrder.getProperty(), joinContext);
                Order order = sortOrder.isAscending() ? cb.asc(path) : cb.desc(path);
                orders.add(order);
            });
            query.orderBy(orders);
        }

        TypedQuery<Long> typedQuery = entityManager.createQuery(query);

        if (pageable.isPaged()) {
            typedQuery.setFirstResult((int) pageable.getOffset());
            typedQuery.setMaxResults(pageable.getPageSize());
        }

        return typedQuery.getResultList().stream().distinct().collect(Collectors.toList());
    }

    /**
     * Registers existing JOINs created by Specifications with the QueryJoinContext.
     * This allows the sorting logic to reuse these JOINs instead of creating duplicates.
     */
    private void registerExistingJoins(Root<T> root, QueryJoinContext joinContext) {
        // Get all joins that were created by the Specification
        Set<Join<T, ?>> joins = root.getJoins();

        for (Join<T, ?> join : joins) {
            String joinPath = getJoinPath(join);
            if (joinPath != null) {
                joinContext.addJoin(joinPath, join);

                // Recursively register nested joins
                registerNestedJoins(join, joinPath, joinContext);
            }
        }
    }

    /**
     * Recursively registers nested joins with the context.
     */
    private void registerNestedJoins(Join<?, ?> parentJoin, String parentPath, QueryJoinContext joinContext) {
        Set<?> rawJoins = parentJoin.getJoins();

        for (Object rawJoin : rawJoins) {
            if (rawJoin instanceof Join) {
                Join<?, ?> nestedJoin = (Join<?, ?>) rawJoin;
                String nestedPath = parentPath + "." + getJoinAttributeName(nestedJoin);
                joinContext.addJoin(nestedPath, nestedJoin);

                // Continue recursively for deeper nesting
                registerNestedJoins(nestedJoin, nestedPath, joinContext);
            }
        }
    }

    /**
     * Gets the join path for a given join (e.g., "trip", "transportOrder").
     */
    private String getJoinPath(Join<?, ?> join) {
        return getJoinAttributeName(join);
    }

    /**
     * Gets the attribute name for a join using reflection.
     */
    private String getJoinAttributeName(Join<?, ?> join) {
        try {
            return join.getAttribute().getName();
        } catch (Exception e) {
            return null;
        }
    }

    private long getTotalCount(Specification<T> specification) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<T> root = countQuery.from(getEntityClass());
        
        if (specification != null) {
            Predicate predicate = specification.toPredicate(root, countQuery, cb);
            if (predicate != null) {
                countQuery.where(predicate);
            }
        }
        
        countQuery.select(cb.countDistinct(root));
        
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    /**
     * Handles nested property paths for sorting with shared JOINs to avoid duplicate JOINs
     * (e.g., "trip.code" -> uses existing trip JOIN if available, otherwise creates one)
     */
    private Path<Object> getNestedPathWithSharedJoins(Root<T> root, String property, QueryJoinContext joinContext) {
        if (property.contains(".")) {
            String[] parts = property.split("\\.");

            // Build the join path (all parts except the last one)
            if (parts.length > 1) {
                StringBuilder joinPathBuilder = new StringBuilder();
                for (int i = 0; i < parts.length - 1; i++) {
                    if (i > 0) joinPathBuilder.append(".");
                    joinPathBuilder.append(parts[i]);
                }
                String joinPath = joinPathBuilder.toString();

                // Try to get existing join or create new one
                Join<?, ?> join = joinContext.getOrCreateJoin(joinPath, JoinType.LEFT);

                // Get the final property from the join
                return join.get(parts[parts.length - 1]);
            }
        }

        // Simple property on root entity
        return root.get(property);
    }


}
