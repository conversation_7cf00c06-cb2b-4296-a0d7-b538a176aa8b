package com.dpw.ctms.move.repository.common;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * Generic repository interface for optimized listing operations
 */
public interface OptimizedListingRepository<T> {
    
    /**
     * Optimized method to find entities with pagination that avoids N+1 queries and memory issues
     */
    Page<T> findAllOptimized(Specification<T> specification, Pageable pageable);
    
    /**
     * Fetches entities with all required related data for listing responses
     */
    List<T> findEntitiesWithRelatedDataByIds(List<Long> entityIds);
}
