package com.dpw.ctms.move.request;

import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// TODO: Replace ids with codes where applicable
public class ShipmentListingRequest {
    private Pagination pagination;

    private Sort sort;

    private Filter filter;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Filter {
        private List<String> tripIds;

        private List<String> shipmentIds;

        private List<String> consignmentIds;

        private List<String> tripStatuses;

        private List<String> transportOrderStatuses;

        private List<String> shipmentStatuses;

        private List<String> transportOrderIds;

        private List<String> customerOrderIds;

        private List<String> vendorIds;

        private List<String> vehicleTypes;

        private List<String> vehicleIds;

        private List<String> trailerIds;

        private List<String> vehicleOperatorIds;

        private String originLocationId;

        private String destinationLocationId;

        @Valid
        private DateRange expectedPickupDateRange;

        @Valid
        private DateRange expectedDeliveryDateRange;

        @Valid
        private DateRange actualPickupDateRange;

        @Valid
        private DateRange actualDeliveryDateRange;

        private Boolean isPodAttached;
    }
}
