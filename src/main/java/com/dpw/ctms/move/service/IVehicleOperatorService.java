package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IVehicleOperatorService {
    Map<String, GetOperatorDetailsListResponse> fetchOperatorDetails(
            Map<String, List<String>> taskVehicleOperatorMap);

    void discardVehicleOperatorResources(Set<VehicleOperatorResource> vehicleOperatorResources);
    Set<VehicleOperatorResource> getAllByCodes(Set<String> codes);

    void populateCrpIdInTransportOrder(TransportOrder transportOrder);
}
