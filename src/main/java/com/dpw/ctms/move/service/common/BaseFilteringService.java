package com.dpw.ctms.move.service.common;

import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.tmsutils.exception.TMSException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

import java.util.function.Supplier;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INTERNAL_ERROR;

/**
 * Base class for filtering services that provides consistent error handling
 * and common utility methods for pagination and sorting.
 */
@Slf4j
public abstract class BaseFilteringService {

    // Default pagination values
    protected static final int DEFAULT_PAGE_SIZE = 20;
    protected static final int MAX_PAGE_SIZE = 500;
    protected static final String DEFAULT_SORT_ORDER = "DESC";

    /**
     * Executes a filtering operation with consistent error handling
     */
    protected <T> T execute(Supplier<T> operation, String operationName) {
        try {
            log.info("Starting {} operation", operationName);
            T result = operation.get();
            log.info("Successfully completed {} operation", operationName);
            return result;
        } catch (TMSException e) {
            log.error("TMSException occurred during {}: {}", operationName, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            String message = String.format("Unexpected error occurred during %s: %s", operationName, e.getMessage());
            log.error(message, e);
            throw new TMSException(INTERNAL_ERROR.name(), message);
        }
    }

    /**
     * Creates a Pageable object with validation and defaults
     */
    protected Pageable createPageable(Pagination pagination, Sort sort) {
        int pageNumber = 0;
        int pageSize = DEFAULT_PAGE_SIZE;

        if (pagination != null) {
            pageNumber = Math.max(0, pagination.getPageNo());
            if (pagination.getPageSize() > 0) {
                pageSize = Math.min(pagination.getPageSize(), MAX_PAGE_SIZE);
            }
        }

        org.springframework.data.domain.Sort springSort = createSort(sort);
        return PageRequest.of(pageNumber, pageSize, springSort);
    }

    /**
     * Creates Sort object with validation and defaults
     * This method should be overridden by subclasses to provide entity-specific sort field mapping
     *
     * @param sortRequest Sort request
     * @return Configured Sort object
     */
    protected org.springframework.data.domain.Sort createSort(Sort sortRequest) {
        String sortBy = getDefaultSortField();
        org.springframework.data.domain.Sort.Direction direction = 
            org.springframework.data.domain.Sort.Direction.fromString(DEFAULT_SORT_ORDER);

        if (sortRequest != null) {
            if (StringUtils.hasText(sortRequest.getSortBy())) {
                sortBy = validateAndMapSortField(sortRequest.getSortBy());
            }

            if (StringUtils.hasText(sortRequest.getSortOrder())) {
                try {
                    direction = org.springframework.data.domain.Sort.Direction.fromString(sortRequest.getSortOrder());
                } catch (IllegalArgumentException e) {
                    log.error("Invalid sort order: {}, using default {}", sortRequest.getSortOrder(), DEFAULT_SORT_ORDER);
                }
            }
        }

        return org.springframework.data.domain.Sort.by(direction, sortBy);
    }

    /**
     * Get the default sort field for this entity
     *
     * @return Default sort field name
     */
    protected abstract String getDefaultSortField();

    /**
     * Validate and map API sort field names to entity field names
     *
     * @param sortBy API sort field name
     * @return Mapped entity field name
     */
    protected abstract String validateAndMapSortField(String sortBy);
}
