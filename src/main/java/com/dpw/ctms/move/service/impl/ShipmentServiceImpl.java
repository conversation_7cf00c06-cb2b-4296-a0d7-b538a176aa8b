package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.mapper.ShipmentViewMapper;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.response.ShipmentViewResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ShipmentFilteringService;
import com.dpw.tmsutils.exception.TMSException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_SHIPMENT_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_SHIPMENT_ID;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

import java.util.List;
import java.util.Objects;
import java.util.Set;


@Slf4j
@Service
@RequiredArgsConstructor
public class ShipmentServiceImpl implements IShipmentService {

    private final ShipmentFilteringService shipmentFilteringService;
    private final ShipmentRepository shipmentRepository;
    private final ShipmentViewMapper shipmentViewMapper;


    @Override
    public Shipment findShipmentById(Long id) {
        return shipmentRepository.findById(id).orElseThrow(() -> {
            log.error("Shipment id {} not found", id);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_SHIPMENT_ID, id)
            );
        });
    }

    @Override
    public ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest) {
        return shipmentFilteringService.filterShipments(shipmentListingRequest);
    }

    @Override
    public List<Shipment> getAllByCodes(Set<String> codes) {
        return shipmentRepository.findAllByCodeInAndDeletedAtIsNull(codes);
    }

    @Override
    public Shipment saveShipment(Shipment entity) {
        return shipmentRepository.save(entity);
    }

    @Override
    public Shipment findShipmentByCode(String code) {
        return shipmentRepository.findByCodeAndDeletedAtIsNull(code).orElseThrow(() -> {
            log.error("Shipment code {} not found", code);
            return new TMSException(
                    DATA_NOT_FOUND.name(),
                    String.format(INVALID_SHIPMENT_CODE, code)
            );
        });
    }

    @Override
    @Transactional
    public ShipmentViewResponse getShipmentView(String shipmentCode) {
        Shipment shipment = getShipmentByCodeWithAllDetails(shipmentCode);
        return shipmentViewMapper.toResponse(shipment);
    }
    @Override
    public void discardShipments(Set<Shipment> shipments) {
        shipments.stream()
                .filter(Objects::nonNull)
                .forEach(shipment -> {shipment.setStatus(ShipmentStatus.DISCARDED);
                    shipment.setDeletedAt(System.currentTimeMillis());
                });
    }

    @Override
    public boolean areShipmentsCancellable(List<Shipment> shipments) {
        return shipments.stream()
                .allMatch(shipment ->
                        shipment.getStatus().equals(ShipmentStatus.ASSIGNED) ||
                                shipment.getStatus().equals(ShipmentStatus.ALLOCATED));
    }

    private Shipment getShipmentByCodeWithAllDetails(String shipmentCode) {
        return shipmentRepository.findByCodeAndDeletedAtIsNull(shipmentCode)
                .orElseThrow(() -> new TMSException(INVALID_REQUEST.name(), "Shipment not found with code: " + shipmentCode));
    }
}

