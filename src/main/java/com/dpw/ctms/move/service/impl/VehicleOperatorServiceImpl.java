package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.repository.VehicleOperatorResourceRepository;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.integration.request.resource.GetOperatorListRequest;
import com.dpw.ctms.move.service.IVehicleOperatorService;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class VehicleOperatorServiceImpl implements IVehicleOperatorService {
    private final ResourceServiceAdapter resourceServiceAdapter;
    private final VehicleOperatorResourceRepository vehicleOperatorResourceRepository;
    private final TaskParamService taskParamService;

    @Override
    public Map<String, GetOperatorDetailsListResponse> fetchOperatorDetails(
            Map<String, List<String>> taskVehicleOperatorMap) {

        Set<String> allOperatorIds = taskVehicleOperatorMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        if (allOperatorIds.isEmpty()) {
            log.warn("No vehicle operators found for tasks");
            return Collections.emptyMap();
        }

        try {
            List<Long> operatorIdsAsLong = allOperatorIds.stream()
                    .map(Long::valueOf)
                    .toList();

            GetOperatorListRequest request = GetOperatorListRequest.builder()
                    .filter(GetOperatorListRequest.GetOperatorListFilter.builder()
                            .ids(operatorIdsAsLong)
                            .build())
                    .pagination(Pagination.builder()
                            .pageNo(0)
                            .pageSize(allOperatorIds.size())
                            .build())
                    .build();

            return resourceServiceAdapter.getOperatorDetailsMap(request);

        } catch (NumberFormatException e) {
            log.error("Invalid operator ID format in task vehicle operators: {}", allOperatorIds, e);
            throw new TMSException("INVALID_OPERATOR_ID", "Invalid operator ID format");
        }
    }

    @Override
    public void discardVehicleOperatorResources(Set<VehicleOperatorResource> vehicleOperatorResources) {
        vehicleOperatorResources.forEach(vehicleOperatorResource ->
                vehicleOperatorResource.setDeletedAt(System.currentTimeMillis()));
    }

    @Override
    public Set<VehicleOperatorResource> getAllByCodes(Set<String> codes) {
        return new HashSet<>(vehicleOperatorResourceRepository.findAllByCodeInAndDeletedAtIsNull(codes));
    }

    @Override
    public void populateCrpIdInTransportOrder(TransportOrder transportOrder) {
        if (transportOrder == null) {
            log.info("Transport order is null, cannot populate crpId");
            return;
        }

        // Step 1: Extract vehicle operators from transport order
        Map<String, List<String>> vehicleOperatorMap = extractVehicleOperatorsFromTransportOrder(transportOrder);

        if (vehicleOperatorMap.isEmpty()) {
            log.info("No vehicle operators found in transport order, skipping crpId population");
            return;
        }

        // Step 2: Fetch operator details in batch
        Map<String, GetOperatorDetailsListResponse> operatorDetailsMap = fetchOperatorDetails(vehicleOperatorMap);

        // Step 3: Populate crpId in VehicleOperatorResource entities
        populateCrpIdInVehicleOperatorResources(transportOrder, operatorDetailsMap);

        log.info("Successfully populated crpId for transport order: {}", transportOrder.getCode());
    }

    private Map<String, List<String>> extractVehicleOperatorsFromTransportOrder(TransportOrder transportOrder) {
        Map<String, List<String>> vehicleOperatorMap = new HashMap<>();

        if (transportOrder.getTrips() == null) {
            log.info("No trips found in transport order");
            return vehicleOperatorMap;
        }

        transportOrder.getTrips().forEach(trip -> {
            if (trip == null) {
                log.info("Null trip found in transport order, skipping");
                return;
            }

            if (trip.getVehicleOperatorResources() != null) {
                List<String> operatorIds = trip.getVehicleOperatorResources().stream()
                        .filter(Objects::nonNull)
                        .map(VehicleOperatorResource::getExternalResourceId)
                        .filter(Objects::nonNull)
                        .toList();

                if (!operatorIds.isEmpty()) {
                    vehicleOperatorMap.put(trip.getCode(), operatorIds);
                }
            }
        });

        return vehicleOperatorMap;
    }

    private void populateCrpIdInVehicleOperatorResources(
            TransportOrder transportOrder,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        if (transportOrder.getTrips() == null) {
            log.info("No trips found in transport order");
            return;
        }

        transportOrder.getTrips().forEach(trip -> {
            if (trip == null) {
                log.info("Null trip found in transport order, skipping");
                return;
            }

            if (trip.getVehicleOperatorResources() != null) {
                trip.getVehicleOperatorResources().forEach(vehicleOperatorResource -> {
                    if (vehicleOperatorResource == null) {
                        log.info("Null vehicle operator resource found, skipping");
                        return;
                    }

                    String crpId = extractCrpIdFromOperatorDetails(
                            vehicleOperatorResource.getExternalResourceId(), operatorDetailsMap);
                    vehicleOperatorResource.setCrpId(crpId);
                    log.info("Populated crpId {} for vehicle operator resource {}",
                            crpId, vehicleOperatorResource.getExternalResourceId());
                });
            }
        });
    }

    private String extractCrpIdFromOperatorDetails(
            String externalResourceId,
            Map<String, GetOperatorDetailsListResponse> operatorDetailsMap) {

        if (externalResourceId == null) {
            log.info("External resource ID is null, cannot extract crpId");
            return null;
        }

        // Find operator details by external resource ID
        GetOperatorDetailsListResponse operatorDetails = operatorDetailsMap.values().stream()
                .filter(details -> externalResourceId.equals(details.getId() != null ? details.getId().toString() : null))
                .findFirst()
                .orElse(null);

        if (operatorDetails == null) {
            log.info("Operator details not found for external resource ID: {}", externalResourceId);
            return null;
        }

        if (operatorDetails.getCrpDetails() == null) {
            log.info("CRP details not found for operator with external resource ID: {}", externalResourceId);
            return null;
        }

        return operatorDetails.getCrpDetails().getCrpUserUUID();
    }

}
