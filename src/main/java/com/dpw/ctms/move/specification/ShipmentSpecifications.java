package com.dpw.ctms.move.specification;

import com.dpw.ctms.move.constants.ShipmentFieldConstants;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.request.DateRange;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dpw.ctms.move.constants.ShipmentFieldConstants.CODE;
import static com.dpw.ctms.move.constants.ShipmentFieldConstants.DELETED_AT;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ShipmentSpecifications {

    public static Specification<Shipment> notDeleted() {
        return (root, query, builder) -> builder.isNull(root.get(DELETED_AT));
    }

    public static Specification<Shipment> hasCode(String code) {
        return (root, query, builder) -> builder.equal(root.get(CODE), code);
    }

    public static Specification<Shipment> codeIn(Set<String> codes) {
        return (root, query, builder) -> root.get(CODE).in(codes);
    }

    public static Specification<Shipment> tripIdsIn(List<String> tripIds) {
        return (root, query, builder) -> {
            List<String> validTripIds = tripIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid trip id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTripIds.isEmpty()) {
                log.info("No valid trip ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            return builder.and(
                    tripJoin.get(CODE).in(validTripIds),
                    builder.isNull(tripJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> shipmentIdsIn(List<String> shipmentIds) {
        return (root, query, builder) -> {
            List<String> validShipmentIds = shipmentIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid shipment id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validShipmentIds.isEmpty()) {
                log.info("No valid shipment ids found");
                return builder.disjunction();
            }

            return root.get(CODE).in(validShipmentIds);
        };
    }

    public static Specification<Shipment> tripStatusesIn(List<String> tripStatuses) {
        return (root, query, builder) -> {
            List<TripStatus> statuses = tripStatuses.stream()
                    .map(status -> {
                        try {
                            return TripStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid trip status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (statuses.isEmpty()) {
                log.info("No valid trip statuses found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            return builder.and(
                    tripJoin.get(ShipmentFieldConstants.STATUS).in(statuses),
                    builder.isNull(tripJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> transportOrderStatusesIn(List<String> transportOrderStatuses) {
        return (root, query, builder) -> {
            List<TransportOrderStatus> statuses = transportOrderStatuses.stream()
                    .map(status -> {
                        try {
                            return TransportOrderStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid transport order status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (statuses.isEmpty()) {
                log.info("No valid transport order statuses found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return builder.and(
                    transportOrderJoin.get(ShipmentFieldConstants.STATUS).in(statuses),
                    builder.isNull(transportOrderJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> shipmentStatusesIn(List<String> shipmentStatuses) {
        return (root, query, builder) -> {
            List<ShipmentStatus> statuses = shipmentStatuses.stream()
                    .map(status -> {
                        try {
                            return ShipmentStatus.valueOf(status.toUpperCase());
                        } catch (IllegalArgumentException e) {
                            log.info("Invalid shipment status: {}", status);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return statuses.isEmpty() ?
                    builder.disjunction() : root.get(ShipmentFieldConstants.STATUS).in(statuses);
        };
    }

    public static Specification<Shipment> transportOrderIdsIn(List<String> transportOrderIds) {
        return (root, query, builder) -> {
            List<String> validTransportOrderIds = transportOrderIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid transport order id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTransportOrderIds.isEmpty()) {
                log.info("No valid transport order ids found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return builder.and(
                    transportOrderJoin.get(CODE).in(validTransportOrderIds),
                    builder.isNull(transportOrderJoin.get(DELETED_AT))
            );
        };
    }



    public static Specification<Shipment> consignmentIdsIn(List<String> consignmentIds) {
        return (root, query, builder) -> {
            List<String> validConsignmentIds = consignmentIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid consignment id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validConsignmentIds.isEmpty()) {
                log.info("No valid consignment ids found");
                return builder.disjunction();
            }

            return root.get(ShipmentFieldConstants.EXTERNAL_CONSIGNMENT_ID).in(validConsignmentIds);
        };
    }



    public static Specification<Shipment> customerOrderIdsIn(List<String> customerOrderIds) {
        return (root, query, builder) -> {
            List<String> validCustomerOrderIds = customerOrderIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid customer order id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validCustomerOrderIds.isEmpty()) {
                log.info("No valid customer order ids found");
                return builder.disjunction();
            }

            return root.get(ShipmentFieldConstants.EXTERNAL_CUSTOMER_ORDER_ID).in(validCustomerOrderIds);
        };
    }

    public static Specification<Shipment> vendorIdsIn(List<String> vendorIds) {
        return (root, query, builder) -> {
            List<String> validVendorIds = vendorIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid vendor id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validVendorIds.isEmpty()) {
                log.info("No valid vendor ids found");
                return builder.disjunction();
            }

            Join<Shipment, TransportOrder> transportOrderJoin = root.join(ShipmentFieldConstants.TRANSPORT_ORDER, JoinType.INNER);
            return builder.and(
                    transportOrderJoin.get(ShipmentFieldConstants.ASSIGNEE_IDENTIFIER).in(validVendorIds),
                    builder.isNull(transportOrderJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> vehicleTypesIn(List<String> vehicleTypes) {
        return (root, query, builder) -> {
            List<String> validVehicleTypes = vehicleTypes.stream()
                    .filter(type -> {
                        if (!StringUtils.hasText(type)) {
                            log.info("Invalid vehicle type: {}", type);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validVehicleTypes.isEmpty()) {
                log.info("No valid vehicle types found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleResource> vehicleJoin = tripJoin.join(ShipmentFieldConstants.VEHICLE_RESOURCE, JoinType.LEFT);
            return builder.and(
                    vehicleJoin.get(ShipmentFieldConstants.EXTERNAL_VEHICLE_TYPE_ID).in(validVehicleTypes),
                    builder.isNull(tripJoin.get(DELETED_AT)),
                    builder.isNull(vehicleJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> vehicleIdsIn(List<String> vehicleIds) {
        return (root, query, builder) -> {
            List<String> validVehicleIds = vehicleIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid vehicle id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validVehicleIds.isEmpty()) {
                log.info("No valid vehicle ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleResource> vehicleJoin = tripJoin.join(ShipmentFieldConstants.VEHICLE_RESOURCE, JoinType.LEFT);
            return builder.and(
                    vehicleJoin.get(ShipmentFieldConstants.EXTERNAL_RESOURCE_ID).in(validVehicleIds),
                    builder.isNull(tripJoin.get(DELETED_AT)),
                    builder.isNull(vehicleJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> trailerIdsIn(List<String> trailerIds) {
        return (root, query, builder) -> {
            List<String> validTrailerIds = trailerIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid trailer id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validTrailerIds.isEmpty()) {
                log.info("No valid trailer ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, TrailerResource> trailerJoin = tripJoin.join(ShipmentFieldConstants.TRAILER_RESOURCES, JoinType.INNER);
            return builder.and(
                    trailerJoin.get(ShipmentFieldConstants.EXTERNAL_RESOURCE_ID).in(validTrailerIds),
                    builder.isNull(tripJoin.get(DELETED_AT)),
                    builder.isNull(trailerJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> vehicleOperatorIdsIn(List<String> vehicleOperatorIds) {
        return (root, query, builder) -> {
            List<String> validOperatorIds = vehicleOperatorIds.stream()
                    .filter(id -> {
                        if (!StringUtils.hasText(id)) {
                            log.info("Invalid vehicle operator id: {}", id);
                            return false;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            if (validOperatorIds.isEmpty()) {
                log.info("No valid vehicle operator ids found");
                return builder.disjunction();
            }

            Join<Shipment, Trip> tripJoin = root.join(ShipmentFieldConstants.TRIP, JoinType.INNER);
            Join<Trip, VehicleOperatorResource> operatorJoin = tripJoin.join(ShipmentFieldConstants.VEHICLE_OPERATOR_RESOURCES, JoinType.INNER);
            return builder.and(
                    operatorJoin.get(ShipmentFieldConstants.EXTERNAL_RESOURCE_ID).in(validOperatorIds),
                    builder.isNull(tripJoin.get(DELETED_AT)),
                    builder.isNull(operatorJoin.get(DELETED_AT))
            );
        };
    }

    public static Specification<Shipment> originLocationIdEquals(String originLocationId) {
        return (root, query, builder) -> {
            Join<Shipment, Stop> originStopJoin = root.join(ShipmentFieldConstants.ORIGIN_STOP, JoinType.INNER);
            return builder.equal(originStopJoin.get(ShipmentFieldConstants.EXTERNAL_LOCATION_CODE), originLocationId);
        };
    }

    public static Specification<Shipment> destinationLocationIdEquals(String destinationLocationId) {
        return (root, query, builder) -> {
            Join<Shipment, Stop> destinationStopJoin = root.join(ShipmentFieldConstants.DESTINATION_STOP, JoinType.INNER);
            return builder.equal(destinationStopJoin.get(ShipmentFieldConstants.EXTERNAL_LOCATION_CODE), destinationLocationId);
        };
    }

    public static Specification<Shipment> isDocumentAttachedFilter(Boolean isDocumentAttached) {
        return (root, query, builder) -> {
            if (isDocumentAttached == null) {
                return builder.conjunction();
            }
            return builder.equal(root.get(ShipmentFieldConstants.IS_DOCUMENT_ATTACHED), isDocumentAttached);
        };
    }

    public static Specification<Shipment> taskCodeEquals(String taskCode) {
        return (root, query, builder) -> {
            if (!StringUtils.hasText(taskCode)) {
                return builder.disjunction();
            }
            // Join Shipment -> ShipmentTask -> Task
            Join<Shipment, ShipmentTask> shipmentTaskJoin = root.join(ShipmentFieldConstants.SHIPMENT_TASKS, JoinType.INNER);
            Join<ShipmentTask, Task> taskJoin = shipmentTaskJoin.join(ShipmentFieldConstants.TASK, JoinType.INNER);
            return builder.equal(taskJoin.get(CODE), taskCode);
        };
    }

    public static Specification<Shipment> dateInRange(String fieldName, DateRange dateRange) {
        return (root, query, builder) -> {
            if (dateRange.getFrom() != null && dateRange.getTo() != null) {
                return builder.and(
                        builder.greaterThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getFrom()),
                        builder.lessThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getTo())
                );
            } else if (dateRange.getFrom() != null) {
                return builder.greaterThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getFrom());
            } else if (dateRange.getTo() != null) {
                return builder.lessThanOrEqualTo(root.get(fieldName).get("epoch"), dateRange.getTo());
            }

            return builder.conjunction();
        };
    }
}