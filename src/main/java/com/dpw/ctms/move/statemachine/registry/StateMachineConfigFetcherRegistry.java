package com.dpw.ctms.move.statemachine.registry;

import com.dpw.ctms.move.dto.StateTransitionHolderDTO;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.statemachine.IStateMachineConfigFetcher;
import com.dpw.ctms.move.statemachine.impl.ShipmentStateMachineConfigFetcher;
import com.dpw.ctms.move.statemachine.impl.TaskStateMachineConfigFetcher;
import com.dpw.ctms.move.statemachine.impl.TransportOrderStateMachineConfigFetcher;
import com.dpw.ctms.move.statemachine.impl.TripStateMachineConfigFetcher;
import com.dpw.tmsutils.exception.TMSException;
import com.dpw.tmsutils.utils.TMSExceptionErrorCode;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.UNSUPPORTED_STATE_MACHINE_TYPE;

@Component
@RequiredArgsConstructor
@Slf4j
public class StateMachineConfigFetcherRegistry {

    private final TaskStateMachineConfigFetcher taskStateMachineConfigFetcher;
    private final ShipmentStateMachineConfigFetcher shipmentStateMachineConfigFetcher;
    private final TripStateMachineConfigFetcher tripStateMachineConfigFetcher;
    private final TransportOrderStateMachineConfigFetcher transportOrderStateMachineConfigFetcher;

    private final Map<StateMachineEntityType, IStateMachineConfigFetcher> fetcherMap = new HashMap<>();

    @PostConstruct
    private void init() {
        fetcherMap.put(StateMachineEntityType.TASK, taskStateMachineConfigFetcher);
        fetcherMap.put(StateMachineEntityType.SHIPMENT, shipmentStateMachineConfigFetcher);
        fetcherMap.put(StateMachineEntityType.TRIP, tripStateMachineConfigFetcher);
        fetcherMap.put(StateMachineEntityType.TRANSPORT_ORDER, transportOrderStateMachineConfigFetcher);
    }

    public StateTransitionHolderDTO getStateMachineConfig(StateMachineEntityType type, String tenantId) {
        IStateMachineConfigFetcher fetcher = fetcherMap.get(type);
        if (fetcher == null) {
            log.error("Unsupported state machine type: {}", type);
            throw new TMSException(TMSExceptionErrorCode.INVALID_DATA.name(),
                    String.format(UNSUPPORTED_STATE_MACHINE_TYPE, type));
        }
        return fetcher.fetchStateMachineConfig(tenantId);
    }
}

