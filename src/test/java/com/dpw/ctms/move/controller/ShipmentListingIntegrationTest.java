package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.StopStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;

import java.util.Arrays;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.DateRange;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.testcontainers.TestDatabaseManager;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.threadlocal.TenantContext;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;



import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ShipmentListingIntegrationTest extends IntegrationTestBase {

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private TripRepository tripRepository;

    @Autowired
    private TransportOrderRepository transportOrderRepository;

    @Autowired
    private StopRepository stopRepository;

    private String uniqueId;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
        uniqueId = String.valueOf(System.currentTimeMillis());
        cleanup();
        setupComprehensiveTestData();
    }

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        try {
            shipmentRepository.deleteAll();
            tripRepository.deleteAll();
            transportOrderRepository.deleteAll();
            stopRepository.deleteAll();
        } catch (Exception e) {
            System.err.println("Repository cleanup failed, using database cleanup: " + e.getMessage());
            TestDatabaseManager.cleanupCfrSchema();
        }
        TenantContext.clear();
    }

    @Test
    void listShipments_WithComprehensiveFilters_ShouldCoverAllFilteringMethods() throws Exception {
        // Arrange - This single test covers most filtering methods for maximum coverage
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = Faker.createComprehensiveShipmentFilter(uniqueId);
        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
        assertNotNull(response.getData());

        // Verify response structure and mapping
        if (!response.getData().isEmpty()) {
            ShipmentListingResponse shipment = response.getData().get(0);
            assertNotNull(shipment.getCode());
            assertNotNull(shipment.getStatus());
            assertNotNull(shipment.getStatus().getLabel());
            assertNotNull(shipment.getStatus().getValue());
        }
    }

    @Test
    void listShipments_WithDateRangeVariations_ShouldCoverAllDateRangeScenarios() throws Exception {
        // Arrange - Covers from-only, to-only, and both date scenarios
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();

        // Test 1: From-only date range (covers dateInRange with from-only)
        ShipmentListingRequest.Filter filter1 = new ShipmentListingRequest.Filter();
        filter1.setExpectedPickupDateRange(new DateRange(currentTime - 86400000L, null));
        MvcResult result1 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(filter1));
        ListResponse<ShipmentListingResponse> response1 = parseResponse(result1);
        assertNotNull(response1);

        // Test 2: To-only date range (covers dateInRange with to-only)
        ShipmentListingRequest.Filter filter2 = new ShipmentListingRequest.Filter();
        filter2.setExpectedDeliveryDateRange(new DateRange(null, currentTime + 86400000L));
        MvcResult result2 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(filter2));
        ListResponse<ShipmentListingResponse> response2 = parseResponse(result2);
        assertNotNull(response2);

        // Test 3: Empty date range (covers dateInRange with null values)
        ShipmentListingRequest.Filter filter3 = new ShipmentListingRequest.Filter();
        filter3.setExpectedPickupDateRange(new DateRange(null, null));
        MvcResult result3 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(filter3));
        ListResponse<ShipmentListingResponse> response3 = parseResponse(result3);
        assertNotNull(response3);
        assertEquals(3, response3.getTotalRecords(), "Empty date range should return all shipments");
    }

    @Test
    void listShipments_WithInvalidDataHandling_ShouldCoverErrorPaths() throws Exception {
        // Arrange - This test covers all invalid data handling paths for maximum coverage
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = Faker.createInvalidDataShipmentFilter();
        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should handle invalid data gracefully and return results for valid data only
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithPaginationAndSorting_ShouldCoverPaginationLogic() throws Exception {
        // Arrange - Covers createPageable method and pagination logic
        TenantContext.setCurrentTenant("CFR");

        // Test large page size (should be limited)
        Pagination largePagination = new Pagination(0, 1000);
        Sort sort = new Sort("code", "DESC");
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(largePagination, sort, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getData().size() <= 500, "Page size should be limited to 500");

        // Verify sorting if there are multiple results
        if (response.getData().size() > 1) {
            String firstCode = response.getData().get(0).getCode();
            String secondCode = response.getData().get(1).getCode();
            assertTrue(firstCode.compareTo(secondCode) >= 0, "Results should be sorted by code in descending order");
        }
    }

    @Test
    void listShipments_WithEmptyCollectionsAndEdgeCases_ShouldCoverRemainingPaths() throws Exception {
        // Arrange - Covers empty collections and edge cases
        TenantContext.setCurrentTenant("CFR");

        // Test 1: Empty collections (covers empty collection handling)
        ShipmentListingRequest.Filter emptyFilter = Faker.createEmptyCollectionsShipmentFilter();
        MvcResult result1 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(emptyFilter));
        ListResponse<ShipmentListingResponse> response1 = parseResponse(result1);
        assertNotNull(response1);
        assertEquals(3, response1.getTotalRecords(), "Empty collections should return all shipments");

        // Test 2: Null filter (covers null filter handling)
        ShipmentListingRequest requestWithNullFilter = Faker.createShipmentListingRequestWithFilter(null);
        MvcResult result2 = performListShipmentsRequest(requestWithNullFilter);
        ListResponse<ShipmentListingResponse> response2 = parseResponse(result2);
        assertNotNull(response2);
        assertEquals(3, response2.getTotalRecords(), "Null filter should return all shipments");
    }

    @Test
    void listShipments_WithNullRequestBody_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/v1/shipments/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("null"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void listShipments_WithVehicleResourceFilters_ShouldCoverVehicleFilteringMethods() throws Exception {
        // Arrange - Covers vehicleTypesIn, vehicleIdsIn, trailerIdsIn, vehicleOperatorIdsIn
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setVehicleTypes(Arrays.asList("TRUCK", "VAN"));
        filter.setVehicleIds(Arrays.asList("VEH_001", "VEH_002"));
        filter.setTrailerIds(Arrays.asList("TRAILER_001", "TRAILER_002"));
        filter.setVehicleOperatorIds(Arrays.asList("OP_001", "OP_002"));
        filter.setVendorIds(Arrays.asList("VENDOR_BASIC", "VENDOR_COMPLEX"));

        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithAllDateRangeFilters_ShouldCoverAllDateFilterMethods() throws Exception {
        // Arrange - Covers all four date range filters
        TenantContext.setCurrentTenant("CFR");
        long currentTime = System.currentTimeMillis();

        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setExpectedPickupDateRange(new DateRange(currentTime - 86400000L, currentTime + 86400000L));
        filter.setExpectedDeliveryDateRange(new DateRange(currentTime, currentTime + 172800000L));
        filter.setActualPickupDateRange(new DateRange(currentTime - 43200000L, null));
        filter.setActualDeliveryDateRange(new DateRange(null, currentTime + 259200000L));

        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithPodAttachedFilter_ShouldCoverPodFilterMethod() throws Exception {
        // Arrange - Covers isPodAttachedFilter method
        TenantContext.setCurrentTenant("CFR");

        // Test with isPodAttached = true
        ShipmentListingRequest.Filter filter1 = new ShipmentListingRequest.Filter();
        filter1.setIsPodAttached(true);
        MvcResult result1 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(filter1));
        ListResponse<ShipmentListingResponse> response1 = parseResponse(result1);
        assertNotNull(response1);

        // Test with isPodAttached = false
        ShipmentListingRequest.Filter filter2 = new ShipmentListingRequest.Filter();
        filter2.setIsPodAttached(false);
        MvcResult result2 = performListShipmentsRequest(Faker.createShipmentListingRequestWithFilter(filter2));
        ListResponse<ShipmentListingResponse> response2 = parseResponse(result2);
        assertNotNull(response2);
    }

    @Test
    void listShipments_WithAllSortFields_ShouldCoverSortFieldMapping() throws Exception {
        // Arrange - Covers validateAndMapSortField method with all valid sort fields
        TenantContext.setCurrentTenant("CFR");
        String[] sortFields = {
                "code", "status", "createdAt", "updatedAt", "tripCode", "transportOrderCode",
                "customerOrderId", "consignmentId", "expectedPickupAt", "expectedDeliveryAt",
                "actualPickupAt", "actualDeliveryAt", "volume", "weight"
        };

        for (String sortField : sortFields) {
            Sort sort = new Sort(sortField, "ASC");
            ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                    new Pagination(0, 10), sort, new ShipmentListingRequest.Filter());

            // Act
            MvcResult result = performListShipmentsRequest(request);

            // Assert
            ListResponse<ShipmentListingResponse> response = parseResponse(result);
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertTrue(response.getTotalRecords() >= 0);
        }
    }

    @Test
    void listShipments_WithInvalidSortField_ShouldUseDefaultSort() throws Exception {
        // Arrange - Covers invalid sort field handling in validateAndMapSortField
        TenantContext.setCurrentTenant("CFR");
        Sort invalidSort = new Sort("invalidField", "ASC");
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                new Pagination(0, 10), invalidSort, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithInvalidSortOrder_ShouldUseDefaultOrder() throws Exception {
        // Arrange - Covers invalid sort order handling in createSort
        TenantContext.setCurrentTenant("CFR");
        Sort invalidOrderSort = new Sort("code", "INVALID_ORDER");
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                new Pagination(0, 10), invalidOrderSort, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithNegativePageNumber_ShouldUseDefaultPageNumber() throws Exception {
        // Arrange - Covers negative page number handling in createPageable
        TenantContext.setCurrentTenant("CFR");
        Pagination negativePagination = new Pagination(-5, 10);
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                negativePagination, new Sort("code", "DESC"), new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithZeroPageSize_ShouldUseDefaultPageSize() throws Exception {
        // Arrange - Covers zero page size handling in createPageable
        TenantContext.setCurrentTenant("CFR");
        Pagination zeroPagination = new Pagination(0, 0);
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                zeroPagination, new Sort("code", "DESC"), new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithNullPaginationAndSort_ShouldUseDefaults() throws Exception {
        // Arrange - Covers null pagination and sort handling
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest request = ShipmentListingRequest.builder()
                .pagination(null)
                .sort(null)
                .filter(new ShipmentListingRequest.Filter())
                .build();

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithEmptyStringFilters_ShouldFilterOutInvalidValues() throws Exception {
        // Arrange - Covers empty string handling in various filter methods
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setOriginLocationId("");
        filter.setDestinationLocationId("   ");
        filter.setShipmentIds(Arrays.asList("", "   ", null, "VALID_SHIPMENT"));
        filter.setTripIds(Arrays.asList("", null, "VALID_TRIP"));
        filter.setConsignmentIds(Arrays.asList("   ", null, "VALID_CONSIGNMENT"));
        filter.setTransportOrderIds(Arrays.asList("", "VALID_TO"));
        filter.setCustomerOrderIds(Arrays.asList(null, "VALID_CO"));
        filter.setVendorIds(Arrays.asList("", "VALID_VENDOR"));
        filter.setVehicleTypes(Arrays.asList("   ", "VALID_TYPE"));
        filter.setVehicleIds(Arrays.asList("", "VALID_VEHICLE"));
        filter.setTrailerIds(Arrays.asList(null, "VALID_TRAILER"));
        filter.setVehicleOperatorIds(Arrays.asList("", "VALID_OPERATOR"));

        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithInvalidStatusValues_ShouldFilterOutInvalidStatuses() throws Exception {
        // Arrange - Covers invalid status handling in status filter methods
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("INVALID_STATUS", "ASSIGNED", "ANOTHER_INVALID"));
        filter.setTripStatuses(Arrays.asList("INVALID_TRIP_STATUS", "CREATED", "BAD_STATUS"));
        filter.setTransportOrderStatuses(Arrays.asList("INVALID_TO_STATUS", "ASSIGNED", "WRONG_STATUS"));

        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithEmptyStatusLists_ShouldReturnDisjunction() throws Exception {
        // Arrange - Covers empty status list handling that returns disjunction
        TenantContext.setCurrentTenant("CFR");
        ShipmentListingRequest.Filter filter = new ShipmentListingRequest.Filter();
        filter.setShipmentStatuses(Arrays.asList("INVALID_STATUS_ONLY"));
        filter.setTripStatuses(Arrays.asList("INVALID_TRIP_STATUS_ONLY"));
        filter.setTransportOrderStatuses(Arrays.asList("INVALID_TO_STATUS_ONLY"));

        ShipmentListingRequest request = Faker.createShipmentListingRequestWithFilter(filter);

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        // Should return 0 results due to disjunction from invalid statuses
        assertEquals(0, response.getTotalRecords());
    }

    @Test
    void listShipments_WithNullSortBy_ShouldUseDefaultSort() throws Exception {
        // Arrange - Covers null sortBy handling in createSort
        TenantContext.setCurrentTenant("CFR");
        Sort nullSortBy = new Sort(null, "ASC");
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                new Pagination(0, 10), nullSortBy, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithEmptySortBy_ShouldUseDefaultSort() throws Exception {
        // Arrange - Covers empty sortBy handling in createSort
        TenantContext.setCurrentTenant("CFR");
        Sort emptySortBy = new Sort("", "ASC");
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                new Pagination(0, 10), emptySortBy, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    @Test
    void listShipments_WithNullSortOrder_ShouldUseDefaultOrder() throws Exception {
        // Arrange - Covers null sortOrder handling in createSort
        TenantContext.setCurrentTenant("CFR");
        Sort nullSortOrder = new Sort("code", null);
        ShipmentListingRequest request = Faker.createCustomShipmentListingRequest(
                new Pagination(0, 10), nullSortOrder, new ShipmentListingRequest.Filter());

        // Act
        MvcResult result = performListShipmentsRequest(request);

        // Assert
        ListResponse<ShipmentListingResponse> response = parseResponse(result);
        assertNotNull(response);
        assertTrue(response.getTotalRecords() >= 0);
    }

    // Helper methods
    private MvcResult performListShipmentsRequest(ShipmentListingRequest request) throws Exception {
        return mockMvc.perform(post("/v1/shipments/list")
                        .header(TENANT_HEADER, "CFR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andReturn();
    }

    private ListResponse<ShipmentListingResponse> parseResponse(MvcResult result) throws Exception {
        String content = result.getResponse().getContentAsString();
        return objectMapper.readValue(content, new TypeReference<ListResponse<ShipmentListingResponse>>() {});
    }

    private void setupComprehensiveTestData() {
        TenantContext.setCurrentTenant("CFR");

        // Create Transport Orders
        TransportOrder basicTO = new TransportOrder();
        basicTO.setCode("TO_BASIC_" + uniqueId);
        basicTO.setStatus(TransportOrderStatus.ASSIGNED);
        basicTO.setAssignmentType(AssignmentType.EXTERNAL);
        basicTO.setAssigneeIdentifier("VENDOR_BASIC");
        basicTO = transportOrderRepository.save(basicTO);

        TransportOrder complexTO = new TransportOrder();
        complexTO.setCode("TO_COMPLEX_" + uniqueId);
        complexTO.setStatus(TransportOrderStatus.ASSIGNED);
        complexTO.setAssignmentType(AssignmentType.EXTERNAL);
        complexTO.setAssigneeIdentifier("VENDOR_COMPLEX");
        complexTO = transportOrderRepository.save(complexTO);

        // Create Trips
        Trip basicTrip = new Trip();
        basicTrip.setCode("TRIP_BASIC_1_" + uniqueId);
        basicTrip.setStatus(TripStatus.CREATED);
        basicTrip.setTransportOrder(basicTO);
        basicTrip.setExternalOriginLocationCode("ORIGIN_BASIC");
        basicTrip.setExternalDestinationLocationCode("DEST_BASIC");
        basicTrip = tripRepository.save(basicTrip);

        Trip complexTrip = new Trip();
        complexTrip.setCode("TRIP_COMPLEX_1_" + uniqueId);
        complexTrip.setStatus(TripStatus.CREATED);
        complexTrip.setTransportOrder(complexTO);
        complexTrip.setExternalOriginLocationCode("ORIGIN_COMPLEX");
        complexTrip.setExternalDestinationLocationCode("DEST_COMPLEX");
        complexTrip = tripRepository.save(complexTrip);

        // Create Stops
        Stop originStop = new Stop();
        originStop.setCode("STOP_ORIGIN_" + uniqueId);
        originStop.setExternalLocationCode("ORIGIN_BASIC");
        originStop.setStatus(StopStatus.PLANNED);
        originStop = stopRepository.save(originStop);

        Stop destStop = new Stop();
        destStop.setCode("STOP_DEST_" + uniqueId);
        destStop.setExternalLocationCode("DEST_BASIC");
        destStop.setStatus(StopStatus.PLANNED);
        destStop = stopRepository.save(destStop);

        Stop complexOriginStop = new Stop();
        complexOriginStop.setCode("STOP_COMPLEX_ORIGIN_" + uniqueId);
        complexOriginStop.setExternalLocationCode("ORIGIN_COMPLEX");
        complexOriginStop.setStatus(StopStatus.PLANNED);
        complexOriginStop = stopRepository.save(complexOriginStop);

        // Create Shipments with comprehensive data
        long currentTime = System.currentTimeMillis();

        // Basic Shipment 1
        Shipment shipment1 = new Shipment();
        shipment1.setCode("SHIPMENT_BASIC_1_" + uniqueId);
        shipment1.setStatus(ShipmentStatus.ASSIGNED);
        shipment1.setTrip(basicTrip);
        shipment1.setTransportOrder(basicTO);
        shipment1.setOriginStop(originStop);
        shipment1.setDestinationStop(destStop);
        shipment1.setExternalConsignmentId("CONSIGNMENT_BASIC");
        shipment1.setExternalCustomerOrderId("CO_BASIC");
        shipment1.setExpectedPickupAt(new Time(currentTime, "UTC"));
        shipment1.setExpectedDeliveryAt(new Time(currentTime + 3600000L, "UTC"));
        shipmentRepository.save(shipment1);

        // Complex Shipment 1
        Shipment shipment2 = new Shipment();
        shipment2.setCode("SHIPMENT_COMPLEX_1_" + uniqueId);
        shipment2.setStatus(ShipmentStatus.ALLOCATED);
        shipment2.setTrip(complexTrip);
        shipment2.setTransportOrder(complexTO);
        shipment2.setOriginStop(complexOriginStop);
        shipment2.setDestinationStop(destStop);
        shipment2.setExternalConsignmentId("CONSIGNMENT_COMPLEX_1");
        shipment2.setExternalCustomerOrderId("CO_COMPLEX_1");
        shipment2.setExpectedPickupAt(new Time(currentTime + 1800000L, "UTC"));
        shipment2.setExpectedDeliveryAt(new Time(currentTime + 5400000L, "UTC"));
        shipmentRepository.save(shipment2);

        // Additional Shipment for testing
        Shipment shipment3 = new Shipment();
        shipment3.setCode("SHIPMENT_ADDITIONAL_" + uniqueId);
        shipment3.setStatus(ShipmentStatus.IN_TRANSIT);
        shipment3.setTrip(basicTrip);
        shipment3.setTransportOrder(basicTO);
        shipment3.setOriginStop(originStop);
        shipment3.setDestinationStop(destStop);
        shipment3.setExternalConsignmentId("CONSIGNMENT_ADDITIONAL");
        shipment3.setExternalCustomerOrderId("CO_ADDITIONAL");
        shipment3.setExpectedPickupAt(new Time(currentTime + 3600000L, "UTC"));
        shipment3.setExpectedDeliveryAt(new Time(currentTime + 7200000L, "UTC"));
        shipmentRepository.save(shipment3);
    }
}
