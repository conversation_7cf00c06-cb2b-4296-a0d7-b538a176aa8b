package com.dpw.ctms.move.kafka.consumer.cfr;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.TimeDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorHeaderDTO;
import com.dpw.ctms.move.dto.consumer.IntegratorTaskMessageRequestDTO;
import com.dpw.ctms.move.dto.consumer.cfr.DeliveryTaskMessageDTO;
import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.EntityType;
import com.dpw.ctms.move.enums.StateMachineEntityType;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.enums.TaskStatus;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ITaskService;
import com.dpw.ctms.move.service.ITripService;
import com.dpw.ctms.move.statemachine.IStateMachineService;
import com.dpw.ctms.move.statemachine.StateMachineConfigReader;
import com.dpw.ctms.move.statemachine.registry.StateMachineServiceRegistry;
import com.dpw.tmsutils.utils.ObjectMapperUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest
@Transactional
@AutoConfigureMockMvc
class DeliveryTaskHandlerTest extends BaseTest {
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IShipmentService shipmentService;
    @Autowired
    private ITripService tripService;
    @Autowired
    private DeliveryTaskHandler deliveryTaskHandler;
    @MockBean
    private StateMachineServiceRegistry stateMachineServiceRegistry;
    @MockBean
    private StateMachineConfigReader stateMachineConfigReader;
    @MockBean
    private IDocumentsService documentsService;

    @BeforeEach
    void mockStateMachine() {
        IStateMachineService<?> mockStateMachine = Mockito.mock(IStateMachineService.class);
        Mockito.doNothing().when(mockStateMachine).handleEvent(Mockito.any(), Mockito.any(), Mockito.any());
        when(stateMachineServiceRegistry.getService(Mockito.any(StateMachineEntityType.class)))
                .thenAnswer(invocation -> mockStateMachine);
    }

    @Test
    void testHandle_withValidShipment() {

        Trip trip = new Trip();
        trip.setActualEndAt(null);

        // Create and initialize 2 stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP1");
        shipment.setTrip(trip);
        shipment.setActualDeliveryAt(null);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP1");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK123");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = DeliveryTaskMessageDTO.PercolatedRecordDTO.builder()
                .unloadingCompletionTime(TimeDTO.builder().epoch(1718500000000L).timezone("Asia/Kolkata").build())
                .arrivalTime(TimeDTO.builder().epoch(1718500000000L).timezone("Asia/Kolkata").build())
                .build();
        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK123")
                        .status(TaskStatus.CLOSED.name())
                        .build();
        IntegratorTaskMessageRequestDTO.MessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<DeliveryTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();
        DeliveryTaskMessageDTO deliveryTaskMessageDTO = DeliveryTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(1718600000000L).build())
                .build();
        deliveryTaskHandler.handle(deliveryTaskMessageDTO);
        Shipment updatedShipment = shipmentService.findShipmentById(shipment.getId());
        Trip updatedTrip = tripService.findTripById(trip.getId());
        Long expectedDeliveryEpoch = percolatedRecordDTO.getUnloadingCompletionTime().getEpoch();
        Long expectedEndEpoch = percolatedRecordDTO.getUnloadingCompletionTime().getEpoch();
        assertThat(updatedShipment.getActualDeliveryAt().getEpoch()).isEqualTo(expectedDeliveryEpoch);
        assertThat(updatedTrip.getActualEndAt().getEpoch()).isEqualTo(expectedEndEpoch);
    }

    @Test
    void testUpdateDocument_withTaskStatusClosed_andBolIdentifier() {
        // Setup
        Trip trip = new Trip();

        // Create and initialize stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP001");
        shipment.setTrip(trip);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP001");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK001");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        // Create message with BOL identifier
        String bolIdentifier = "BOL-12345";
        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = DeliveryTaskMessageDTO.PercolatedRecordDTO.builder()
                .bolIdentifier(bolIdentifier)
                .unloadingCompletionTime(TimeDTO.builder().epoch(System.currentTimeMillis()).timezone("Asia/Kolkata").build())
                .build();

        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK001")
                        .status(TaskStatus.CLOSED.name())
                        .build();

        IntegratorTaskMessageRequestDTO.MessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<DeliveryTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();

        DeliveryTaskMessageDTO deliveryTaskMessageDTO = DeliveryTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(System.currentTimeMillis()).build())
                .build();

        // Act
        deliveryTaskHandler.handle(deliveryTaskMessageDTO);

        // Assert
        ArgumentCaptor<DeliveryTaskDocumentDTO> captor = ArgumentCaptor.forClass(DeliveryTaskDocumentDTO.class);
        verify(documentsService).findAndUpdate(captor.capture());

        DeliveryTaskDocumentDTO capturedDto = captor.getValue();
        assertThat(capturedDto.getEntityId()).isEqualTo("TASK001");
        assertThat(capturedDto.getEntityType()).isEqualTo(EntityType.TASK.name());
        assertThat(capturedDto.getAsyncMappingUUID()).isEqualTo(bolIdentifier);
        assertThat(capturedDto.getOperationType()).isEqualTo(DocumentOperationType.UPLOAD);
    }

    @Test
    void testUpdateDocument_withTaskStatusClosed_noBolIdentifier() {
        // Setup
        Trip trip = new Trip();

        // Create and initialize stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP002");
        shipment.setTrip(trip);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP002");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK002");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        // Create message without BOL identifier
        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = DeliveryTaskMessageDTO.PercolatedRecordDTO.builder()
                .bolIdentifier(null) // No BOL identifier
                .unloadingCompletionTime(TimeDTO.builder().epoch(System.currentTimeMillis()).timezone("Asia/Kolkata").build())
                .build();

        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK002")
                        .status(TaskStatus.CLOSED.name())
                        .build();

        IntegratorTaskMessageRequestDTO.MessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<DeliveryTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();

        DeliveryTaskMessageDTO deliveryTaskMessageDTO = DeliveryTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(System.currentTimeMillis()).build())
                .build();

        // Act
        deliveryTaskHandler.handle(deliveryTaskMessageDTO);

        verify(documentsService, never()).findAndUpdate(any(DeliveryTaskDocumentDTO.class));
    }

    @Test
    void testUpdateDocument_withTaskStatusNotClosed() {
        // Setup
        Trip trip = new Trip();

        // Create and initialize stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP003");
        shipment.setTrip(trip);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP003");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK003");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        // Create message with BOL identifier but status is not CLOSED
        String bolIdentifier = "BOL-67890";
        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = DeliveryTaskMessageDTO.PercolatedRecordDTO.builder()
                .bolIdentifier(bolIdentifier)
                .unloadingCompletionTime(TimeDTO.builder().epoch(System.currentTimeMillis()).timezone("Asia/Kolkata").build())
                .build();

        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK003")
                        .status(TaskStatus.COMPLETED.name()) // Not CLOSED
                        .build();

        IntegratorTaskMessageRequestDTO.MessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<DeliveryTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();

        DeliveryTaskMessageDTO deliveryTaskMessageDTO = DeliveryTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(System.currentTimeMillis()).build())
                .build();
        deliveryTaskHandler.handle(deliveryTaskMessageDTO);
        verify(documentsService, never()).findAndUpdate(any(DeliveryTaskDocumentDTO.class));
    }

    @Test
    void testUpdateDocument_withEmptyBolIdentifier() {
        // Setup
        Trip trip = new Trip();


        // Create and initialize stops for the trip
        Stop stop1 = new Stop();
        stop1.setCode("STOP1");
        stop1.setSequence(1);
        stop1.setTrip(trip);

        Stop stop2 = new Stop();
        stop2.setCode("STOP2");
        stop2.setSequence(2);
        stop2.setTrip(trip);

        Set<Stop> stops = new HashSet<>();
        stops.add(stop1);
        stops.add(stop2);
        trip.setStops(stops);

        trip = tripService.saveTrip(trip);

        Shipment shipment = new Shipment();
        shipment.setCode("SHIP004");
        shipment.setTrip(trip);
        shipment = shipmentService.saveShipment(shipment);

        TaskParam taskParam = new TaskParam();
        taskParam.setParamName(TaskParamType.SHIPMENT);
        ParamValueShipmentDTO shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP004");
        taskParam.setParamValue(ObjectMapperUtil.getObjectMapper().valueToTree(shipmentDTO));

        Task task = new Task();
        task.setCode("TASK004");
        task.setTaskParams(new ArrayList<>(List.of(taskParam)));
        taskService.saveTask(task);

        // Create message with empty BOL identifier
        DeliveryTaskMessageDTO.PercolatedRecordDTO percolatedRecordDTO = DeliveryTaskMessageDTO.PercolatedRecordDTO.builder()
                .bolIdentifier("") // Empty string
                .unloadingCompletionTime(TimeDTO.builder().epoch(System.currentTimeMillis()).timezone("Asia/Kolkata").build())
                .build();

        IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO taskDetails =
                IntegratorTaskMessageRequestDTO.IntegratorTaskDetailsRequestDTO.builder()
                        .taskTransactionCode("TASK004")
                        .status(TaskStatus.CLOSED.name())
                        .build();

        IntegratorTaskMessageRequestDTO.MessageRequestDTO<DeliveryTaskMessageDTO.PercolatedRecordDTO> messageDTO =
                IntegratorTaskMessageRequestDTO.MessageRequestDTO.<DeliveryTaskMessageDTO.PercolatedRecordDTO>builder()
                        .percolatedRecords(percolatedRecordDTO)
                        .taskDetails(taskDetails)
                        .build();

        DeliveryTaskMessageDTO deliveryTaskMessageDTO = DeliveryTaskMessageDTO.builder()
                .message(messageDTO)
                .transactionContext(IntegratorHeaderDTO.builder().dateTime(System.currentTimeMillis()).build())
                .build();

        // Act
        deliveryTaskHandler.handle(deliveryTaskMessageDTO);

        // Assert - documentsService should NOT be called with empty string due to StringUtils.hasText()
        verify(documentsService, never()).findAndUpdate(any(DeliveryTaskDocumentDTO.class));
    }
}