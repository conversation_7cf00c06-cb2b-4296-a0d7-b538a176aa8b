package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.document.DeliveryTaskDocumentDTO;
import com.dpw.ctms.move.dto.document.DocumentDTO;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.request.documentEvent.FileMetadata;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.junit.jupiter.api.Assertions.*;

class DocumentMapperTest {

    private final DocumentMapper documentMapper = Mappers.getMapper(DocumentMapper.class);

    @Test
    void toDTO_shouldMapDocumentEntityToDocumentDTO() {
        // Given
        Document document = Document.builder()
                .entityId("entity123")
                .entityType("SHIPMENT")
                .documentType(DocumentType.BOL)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .checksum("checksum123")
                .fileIdentifier("file123")
                .build();

        // When
        DocumentDTO result = documentMapper.toDTO(document);

        // Then
        assertNotNull(result);
        assertEquals("entity123", result.getEntityId());
        assertEquals("SHIPMENT", result.getEntityType());
        assertEquals(DocumentType.BOL, result.getDocumentType());
        assertEquals(DocumentStatus.ACTIVE, result.getStatus());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
        assertEquals("checksum123", result.getChecksum());
        assertEquals("file123", result.getFileIdentifier());
    }

    @Test
    void toEntity_shouldMapDocumentDTOToDocumentEntity() {
        // Given
        DocumentDTO dto = DocumentDTO.builder()
                .entityId("entity456")
                .entityType("TRIP")
                .documentType(DocumentType.MANIFEST)
                .status(DocumentStatus.INACTIVE)
                .documentOperationType(DocumentOperationType.DOWNLOAD)
                .checksum("checksum456")
                .fileIdentifier("file456")
                .build();

        // When
        Document result = documentMapper.toEntity(dto);

        // Then
        assertNotNull(result);
        assertEquals("entity456", result.getEntityId());
        assertEquals("TRIP", result.getEntityType());
        assertEquals(DocumentType.MANIFEST, result.getDocumentType());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
        assertEquals(DocumentOperationType.DOWNLOAD, result.getDocumentOperationType());
        assertEquals("checksum456", result.getChecksum());
        assertEquals("file456", result.getFileIdentifier());
    }

    @Test
    void fromPreSignedUrlEvent_shouldMapPreSignedUrlEventToDocument() {
        // Given
        FileMetadata fileMetadata = FileMetadata.builder()
                .tenant(Tenant.CFR.name())
                .build();

        PreSignedUrlEvent event = PreSignedUrlEvent.builder()
                .clientIdentifier("client123")
                .fileKey("fileKey123")
                .fileIdentifier("file123")
                .fileName("test.pdf")
                .fileSize(1024)
                .fileType("PDF")
                .presignedDownloadUrl("https://example.com/download")
                .fileMetadata(fileMetadata)
                .build();

        // When
        Document result = documentMapper.fromPreSignedUrlEvent(event);

        // Then
        assertNotNull(result);
        assertEquals("client123", result.getClientIdentifier());
        assertEquals("fileKey123", result.getAsyncMappingUUID());
        assertEquals("file123", result.getFileIdentifier());
        assertEquals("test.pdf", result.getFileName());
        assertEquals(1024, result.getFileSize());
        assertEquals("PDF", result.getFileType());
        assertEquals("https://example.com/download", result.getPresignedDownloadUrl());
        assertEquals(fileMetadata, result.getFileMetadata());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
        
        // Ignored fields should be null
        assertNull(result.getEntityId());
        assertNull(result.getEntityType());
        assertNull(result.getDocumentType());
        assertNull(result.getChecksum());
    }

    @Test
    void updateDocumentFromPreSignedUrlEvent_shouldUpdateExistingDocument() {
        // Given
        Document existingDocument = Document.builder()
                .entityId("existing_entity")
                .entityType("EXISTING_TYPE")
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.BOL)
                .checksum("existing_checksum")
                .build();

        FileMetadata newFileMetadata = FileMetadata.builder()
                .tenant(Tenant.CFR.name())
                .build();

        PreSignedUrlEvent event = PreSignedUrlEvent.builder()
                .clientIdentifier("new_client")
                .fileKey("new_fileKey")
                .fileIdentifier("new_file")
                .fileName("new_test.pdf")
                .fileSize(2048)
                .fileType("PDF")
                .presignedDownloadUrl("https://example.com/new-download")
                .fileMetadata(newFileMetadata)
                .build();

        // When
        documentMapper.updateDocumentFromPreSignedUrlEvent(existingDocument, event);

        // Then
        // Updated fields
        assertEquals("new_client", existingDocument.getClientIdentifier());
        assertEquals("new_fileKey", existingDocument.getAsyncMappingUUID());
        assertEquals("new_file", existingDocument.getFileIdentifier());
        assertEquals("new_test.pdf", existingDocument.getFileName());
        assertEquals(2048, existingDocument.getFileSize());
        assertEquals("PDF", existingDocument.getFileType());
        assertEquals("https://example.com/new-download", existingDocument.getPresignedDownloadUrl());
        assertEquals(newFileMetadata, existingDocument.getFileMetadata());
        assertEquals(DocumentOperationType.UPLOAD, existingDocument.getDocumentOperationType());
        
        // Ignored fields should remain unchanged
        assertEquals("existing_entity", existingDocument.getEntityId());
        assertEquals("EXISTING_TYPE", existingDocument.getEntityType());
        assertEquals(DocumentStatus.ACTIVE, existingDocument.getStatus());
        assertEquals(DocumentType.BOL, existingDocument.getDocumentType());
        assertEquals("existing_checksum", existingDocument.getChecksum());
    }

    @Test
    void fromDeliveryTaskDocumentDTO_shouldMapDeliveryTaskDocumentDTOToDocument() {
        // Given
        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUID("delivery_file")
                .entityId("delivery_entity")
                .entityType("SHIPMENT")
                .operationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.POD)
                .build();

        // When
        Document result = documentMapper.fromDeliveryTaskDocumentDTO(dto);

        // Then
        assertNotNull(result);
        assertEquals("delivery_file", result.getAsyncMappingUUID());
        assertEquals("delivery_entity", result.getEntityId());
        assertEquals("SHIPMENT", result.getEntityType());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
        assertEquals(DocumentType.POD, result.getDocumentType());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
    }

    @Test
    void updateDocumentFromDeliveryTaskDTO_shouldUpdateDocumentWithDeliveryTaskData() {
        // Given
        Document existingDocument = Document.builder()
                .fileIdentifier("existing_file")
                .entityId("existing_entity")
                .entityType("EXISTING_TYPE")
                .status(DocumentStatus.ACTIVE)
                .build();

        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUID("new_delivery_file")
                .entityId("new_delivery_entity")
                .entityType("NEW_SHIPMENT")
                .operationType(DocumentOperationType.DOWNLOAD)
                .documentType(DocumentType.MANIFEST)
                .build();

        // When
        documentMapper.updateDocumentFromDeliveryTaskDTO(existingDocument, dto);

        // Then
        // Updated fields
        assertEquals("new_delivery_entity", existingDocument.getEntityId());
        assertEquals("NEW_SHIPMENT", existingDocument.getEntityType());
        
        // Unchanged fields
        assertEquals("existing_file", existingDocument.getFileIdentifier());
        assertEquals(DocumentStatus.ACTIVE, existingDocument.getStatus());
    }

    @Test
    void updateDocument_withPreSignedUrlEvent_shouldUpdateDocumentCorrectly() {
        // Given
        Document existingDocument = Document.builder()
                .entityId("existing_entity")
                .entityType("EXISTING_TYPE")
                .status(DocumentStatus.ACTIVE)
                .documentType(DocumentType.BOL)
                .checksum("existing_checksum")
                .build();

        FileMetadata newFileMetadata = FileMetadata.builder()
                .tenant(Tenant.CFR.name())
                .build();

        PreSignedUrlEvent event = PreSignedUrlEvent.builder()
                .clientIdentifier("updated_client")
                .fileKey("updated_fileKey")
                .fileIdentifier("updated_file")
                .fileName("updated_test.pdf")
                .fileSize(4096)
                .fileType("PDF")
                .presignedDownloadUrl("https://example.com/updated-download")
                .fileMetadata(newFileMetadata)
                .build();

        // When
        documentMapper.updateDocument(existingDocument, event);

        // Then
        // Updated fields
        assertEquals("updated_client", existingDocument.getClientIdentifier());
        assertEquals("updated_fileKey", existingDocument.getAsyncMappingUUID());
        assertEquals("updated_file", existingDocument.getFileIdentifier());
        assertEquals("updated_test.pdf", existingDocument.getFileName());
        assertEquals(4096, existingDocument.getFileSize());
        assertEquals("PDF", existingDocument.getFileType());
        assertEquals("https://example.com/updated-download", existingDocument.getPresignedDownloadUrl());
        assertEquals(newFileMetadata, existingDocument.getFileMetadata());
        assertEquals(DocumentOperationType.UPLOAD, existingDocument.getDocumentOperationType());
        
        // Ignored fields should remain unchanged
        assertEquals("existing_entity", existingDocument.getEntityId());
        assertEquals("EXISTING_TYPE", existingDocument.getEntityType());
        assertEquals(DocumentStatus.ACTIVE, existingDocument.getStatus());
        assertEquals(DocumentType.BOL, existingDocument.getDocumentType());
        assertEquals("existing_checksum", existingDocument.getChecksum());
    }

    @Test
    void updateDocument_withDeliveryTaskDocumentDTO_shouldUpdateDocumentCorrectly() {
        // Given
        Document existingDocument = Document.builder()
                .fileIdentifier("existing_file")
                .entityId("existing_entity")
                .entityType("EXISTING_TYPE")
                .status(DocumentStatus.ACTIVE)
                .build();

        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUID("dto_file")
                .entityId("dto_entity")
                .entityType("DTO_SHIPMENT")
                .operationType(DocumentOperationType.DOWNLOAD)
                .documentType(DocumentType.BOL)
                .build();

        // When
        documentMapper.updateDocument(existingDocument, dto);

        // Then
        // Updated fields
        assertEquals("dto_entity", existingDocument.getEntityId());
        assertEquals("DTO_SHIPMENT", existingDocument.getEntityType());
        
        // Unchanged fields
        assertEquals("existing_file", existingDocument.getFileIdentifier());
        assertEquals(DocumentStatus.ACTIVE, existingDocument.getStatus());
    }

    @Test
    void createDocument_withPreSignedUrlEvent_shouldCreateNewDocument() {
        // Given
        FileMetadata fileMetadata = FileMetadata.builder()
                .tenant(Tenant.CFR.name())
                .build();

        PreSignedUrlEvent event = PreSignedUrlEvent.builder()
                .clientIdentifier("create_client")
                .fileKey("create_fileKey")
                .fileIdentifier("create_file")
                .fileName("create_test.pdf")
                .fileSize(8192)
                .fileType("PDF")
                .presignedDownloadUrl("https://example.com/create-download")
                .fileMetadata(fileMetadata)
                .build();

        // When
        Document result = documentMapper.createDocument(event);

        // Then
        assertNotNull(result);
        assertEquals("create_client", result.getClientIdentifier());
        assertEquals("create_fileKey", result.getAsyncMappingUUID());
        assertEquals("create_file", result.getFileIdentifier());
        assertEquals("create_test.pdf", result.getFileName());
        assertEquals(8192, result.getFileSize());
        assertEquals("PDF", result.getFileType());
        assertEquals("https://example.com/create-download", result.getPresignedDownloadUrl());
        assertEquals(fileMetadata, result.getFileMetadata());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
        
        // Ignored fields should be null
        assertNull(result.getEntityId());
        assertNull(result.getEntityType());
        assertNull(result.getDocumentType());
        assertNull(result.getChecksum());
    }

    @Test
    void createDocument_withDeliveryTaskDocumentDTO_shouldCreateNewDocument() {
        // Given
        DeliveryTaskDocumentDTO dto = DeliveryTaskDocumentDTO.builder()
                .asyncMappingUUID("create_delivery_file")
                .entityId("create_delivery_entity")
                .entityType("CREATE_SHIPMENT")
                .operationType(DocumentOperationType.UPLOAD)
                .documentType(DocumentType.POD)
                .build();

        // When
        Document result = documentMapper.createDocument(dto);

        // Then
        assertNotNull(result);
        assertEquals("create_delivery_file", result.getAsyncMappingUUID());
        assertEquals("create_delivery_entity", result.getEntityId());
        assertEquals("CREATE_SHIPMENT", result.getEntityType());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
        assertEquals(DocumentType.POD, result.getDocumentType());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
    }

    @Test
    void toDTO_shouldHandleNullValues() {
        // Given
        Document document = Document.builder().build();

        // When
        DocumentDTO result = documentMapper.toDTO(document);

        // Then
        assertNotNull(result);
        assertNull(result.getEntityId());
        assertNull(result.getEntityType());
        assertNull(result.getDocumentType());
        assertNull(result.getStatus());
        assertNull(result.getDocumentOperationType());
        assertNull(result.getChecksum());
        assertNull(result.getFileIdentifier());
    }

    @Test
    void toEntity_shouldHandleNullValues() {
        // Given
        DocumentDTO dto = DocumentDTO.builder().build();

        // When
        Document result = documentMapper.toEntity(dto);

        // Then
        assertNotNull(result);
        assertNull(result.getEntityId());
        assertNull(result.getEntityType());
        assertNull(result.getDocumentType());
        assertNull(result.getStatus());
        assertNull(result.getDocumentOperationType());
        assertNull(result.getChecksum());
        assertNull(result.getFileIdentifier());
    }

    @Test
    void createDocument_withNullFileMetadata_shouldHandleGracefully() {
        // Given
        PreSignedUrlEvent event = PreSignedUrlEvent.builder()
                .clientIdentifier("client_no_metadata")
                .fileIdentifier("file_no_metadata")
                .fileName("test_no_metadata.pdf")
                .fileMetadata(null)
                .build();

        // When
        Document result = documentMapper.createDocument(event);

        // Then
        assertNotNull(result);
        assertEquals("client_no_metadata", result.getClientIdentifier());
        assertEquals("file_no_metadata", result.getFileIdentifier());
        assertEquals("test_no_metadata.pdf", result.getFileName());
        assertNull(result.getFileMetadata());
        assertEquals(DocumentStatus.INACTIVE, result.getStatus());
        assertEquals(DocumentOperationType.UPLOAD, result.getDocumentOperationType());
    }
}