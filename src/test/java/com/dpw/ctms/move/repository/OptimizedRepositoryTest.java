package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.repository.common.OptimizedListingRepository;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("local")
@Transactional
public class OptimizedRepositoryTest {

    @Autowired
    private ShipmentRepository shipmentRepository;
    
    @Autowired
    private TripRepository tripRepository;
    
    @Autowired
    private TransportOrderRepository transportOrderRepository;

    @BeforeEach
    void setUp() {
        TenantContext.setCurrentTenant("CFR");
    }

    @Test
    void testShipmentRepositoryImplementsOptimizedInterface() {
        // Verify that ShipmentRepository implements OptimizedListingRepository
        assertTrue(shipmentRepository instanceof OptimizedListingRepository, 
                "ShipmentRepository should implement OptimizedListingRepository");
        
        // Test that the optimized method exists and can be called
        Specification<Shipment> spec = (root, query, builder) -> builder.isNull(root.get("deletedAt"));
        Pageable pageable = PageRequest.of(0, 5);
        
        Page<Shipment> result = shipmentRepository.findAllOptimized(spec, pageable);
        assertNotNull(result, "findAllOptimized should return a result");
        System.out.println("✅ ShipmentRepository.findAllOptimized() works - returned " + result.getTotalElements() + " shipments");
    }

    @Test
    void testTripRepositoryImplementsOptimizedInterface() {
        // Verify that TripRepository implements OptimizedListingRepository
        assertTrue(tripRepository instanceof OptimizedListingRepository, 
                "TripRepository should implement OptimizedListingRepository");
        
        // Test that the optimized method exists and can be called
        Specification<Trip> spec = (root, query, builder) -> builder.isNull(root.get("deletedAt"));
        Pageable pageable = PageRequest.of(0, 5);
        
        Page<Trip> result = tripRepository.findAllOptimized(spec, pageable);
        assertNotNull(result, "findAllOptimized should return a result");
        System.out.println("✅ TripRepository.findAllOptimized() works - returned " + result.getTotalElements() + " trips");
    }

    @Test
    void testTransportOrderRepositoryImplementsOptimizedInterface() {
        // Verify that TransportOrderRepository implements OptimizedListingRepository
        assertTrue(transportOrderRepository instanceof OptimizedListingRepository, 
                "TransportOrderRepository should implement OptimizedListingRepository");
        
        // Test that the optimized method exists and can be called
        Specification<TransportOrder> spec = (root, query, builder) -> builder.isNull(root.get("deletedAt"));
        Pageable pageable = PageRequest.of(0, 5);
        
        Page<TransportOrder> result = transportOrderRepository.findAllOptimized(spec, pageable);
        assertNotNull(result, "findAllOptimized should return a result");
        System.out.println("✅ TransportOrderRepository.findAllOptimized() works - returned " + result.getTotalElements() + " transport orders");
    }
}
