package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.TaskServiceConstants;
import com.dpw.ctms.move.dto.ParamValueVehicleOperatorDTO;
import com.dpw.ctms.move.dto.taskmanager.TaskRegistrationDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.entity.VehicleOperatorResource;
import com.dpw.ctms.move.integration.adapter.TaskServiceAdapter;
import com.dpw.ctms.move.integration.request.taskmanager.TaskInstanceRegistrationRequest;
import com.dpw.ctms.move.integration.response.taskmanager.TaskInstanceRegistrationResponse;
import com.dpw.ctms.move.integration.response.resource.operator.GetOperatorDetailsListResponse;
import com.dpw.ctms.move.mapper.TaskInstanceRegistrationRequestMapper;
import com.dpw.ctms.move.repository.TaskRepository;
import com.dpw.ctms.move.service.impl.TaskParamService;
import com.dpw.ctms.move.service.impl.TaskServiceImpl;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.DATA_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskServiceImplTest {

    @Mock
    private TaskInstanceRegistrationRequestMapper taskInstanceRegistrationRequestMapper;

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private TaskServiceAdapter taskServiceAdapter;

    @Mock
    private TaskParamService taskParamService;

    @Mock
    private IVehicleOperatorService vehicleOperatorService;

    @InjectMocks
    private TaskServiceImpl taskService;

    private Task testTask;
    private TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO testTaskDTO;
    private ParamValueVehicleOperatorDTO testVehicleOperator;
    private GetOperatorDetailsListResponse testOperatorDetails;
    private TransportOrder testTransportOrder;

    @BeforeEach
    void setUp() {
        testTask = new Task();
        testTask.setId(1L);
        testTask.setCode("TASK001");

        testTaskDTO = new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        testTaskDTO.setExtTaskTransactionCode("TASK001");

        testVehicleOperator = new ParamValueVehicleOperatorDTO();
        testVehicleOperator.setExternalResourceId("OP001");

        // Mock CRP details
        GetOperatorDetailsListResponse.CrpDetailsResponse crpDetails = new GetOperatorDetailsListResponse.CrpDetailsResponse();
        crpDetails.setCrpUserUUID("CRP001");

        testOperatorDetails = new GetOperatorDetailsListResponse();
        testOperatorDetails.setCrpDetails(crpDetails);

        testTransportOrder = new TransportOrder();
        testTransportOrder.setId(1L);
        testTransportOrder.setCode("TO001");
    }

    @Test
    void findTaskById_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        Long taskId = 1L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskById(taskId);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getId(), result.getId());
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskById_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        Long taskId = 999L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskById(taskId));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_ID, taskId)));
        verify(taskRepository).findById(taskId);
    }

    @Test
    void findTaskByCode_WhenTaskExists_ShouldReturnTask() {
        // Arrange
        String taskCode = "TASK001";
        when(taskRepository.findByCodeAndDeletedAtIsNull(taskCode)).thenReturn(Optional.of(testTask));

        // Act
        Task result = taskService.findTaskByCode(taskCode);

        // Assert
        assertNotNull(result);
        assertEquals(testTask.getCode(), result.getCode());
        verify(taskRepository).findByCodeAndDeletedAtIsNull(taskCode);
    }

    @Test
    void findTaskByCode_WhenTaskNotExists_ShouldThrowTMSException() {
        // Arrange
        String taskCode = "INVALID_CODE";
        when(taskRepository.findByCodeAndDeletedAtIsNull(taskCode)).thenReturn(Optional.empty());

        // Act & Assert
        TMSException exception = assertThrows(TMSException.class, () -> taskService.findTaskByCode(taskCode));
        assertEquals(DATA_NOT_FOUND.name(), exception.getErrorCode());
//        assertTrue(exception.getMessage().contains(String.format(INVALID_TASK_CODE, taskCode)));
        verify(taskRepository).findByCodeAndDeletedAtIsNull(taskCode);
    }

    @Test
    void saveTask_ShouldReturnSavedTask() {
        // Arrange
        when(taskRepository.save(testTask)).thenReturn(testTask);

        // Act
        Task result = taskService.saveTask(testTask);

        // Assert
        assertNotNull(result);
        assertEquals(testTask, result);
        verify(taskRepository).save(testTask);
    }

    @Test
    void registerTaskInstances_WhenTaskListIsNull_ShouldReturnEarly() {
        // Act
        taskService.registerTaskInstances(null, testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void registerTaskInstances_WhenTaskListIsEmpty_ShouldReturnEarly() {
        // Act
        taskService.registerTaskInstances(Collections.emptyList(), testTransportOrder);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void registerTaskInstances_WhenTransportOrderIsNull_ShouldReturnEarly() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);

        // Act
        taskService.registerTaskInstances(taskList, null);

        // Assert
        verifyNoInteractions(taskParamService);
        verifyNoInteractions(taskServiceAdapter);
        verifyNoInteractions(taskRepository);
    }

    @Test
    void registerTaskInstances_WithValidTasks_ShouldProcessSuccessfully() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);


        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), eq(TaskServiceConstants.DEFAULT_TENANT_CODE),
                eq(TaskServiceConstants.DEFAULT_TENANT_SERVICE_CODE), anyString()))
                .thenReturn(responses);

        // Act
        taskService.registerTaskInstances(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verifyNoInteractions(vehicleOperatorService); // Should not call fetchOperatorDetails anymore
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void registerTaskInstances_WithTasksHavingNoVehicleOperators_ShouldProcessWithControllerRoleOnly() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> emptyVehicleOperators = Collections.emptyList();


        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(emptyVehicleOperators);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.registerTaskInstances(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verifyNoInteractions(vehicleOperatorService); // Should not call fetchOperatorDetails
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
    }

    @Test
    void registerTaskInstances_WithEmptyOperatorDetailsMap_ShouldProcessSuccessfully() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);


        TaskInstanceRegistrationResponse response = new TaskInstanceRegistrationResponse();
        response.setExtTaskTransactionCode("TASK001");
        response.setTaskRegistrationCode("REG001");
        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.registerTaskInstances(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verifyNoInteractions(vehicleOperatorService);
        verify(taskServiceAdapter).registerTaskInstance(any(), anyString(), anyString(), anyString());
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
    }

    @Test
    void registerTaskInstances_WithMultipleTasks_ShouldProcessAllTasks() {
        // Arrange
        Task task2 = new Task();
        task2.setId(2L);
        task2.setCode("TASK002");

        List<Task> taskList = Arrays.asList(testTask, task2);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);


        TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO task2DTO =
                new TaskRegistrationDTO.TaskInstanceRegisterDetailsDTO();
        task2DTO.setExtTaskTransactionCode("TASK002");

        TaskInstanceRegistrationResponse response1 = new TaskInstanceRegistrationResponse();
        response1.setExtTaskTransactionCode("TASK001");
        response1.setTaskRegistrationCode("REG001");

        TaskInstanceRegistrationResponse response2 = new TaskInstanceRegistrationResponse();
        response2.setExtTaskTransactionCode("TASK002");
        response2.setTaskRegistrationCode("REG002");

        List<TaskInstanceRegistrationResponse> responses = Arrays.asList(response1, response2);

        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskParamService.getVehicleOperators(task2)).thenReturn(Collections.emptyList());
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(task2))
                .thenReturn(task2DTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenReturn(responses);

        // Act
        taskService.registerTaskInstances(taskList, testTransportOrder);

        // Assert
        verify(taskParamService).getVehicleOperators(testTask);
        verify(taskParamService).getVehicleOperators(task2);
        verifyNoInteractions(vehicleOperatorService); // Should not call fetchOperatorDetails
        assertEquals("REG001", testTask.getExternalTaskRegistrationCode());
        assertEquals("REG002", task2.getExternalTaskRegistrationCode());
    }

    @Test
    void registerTaskInstances_WithTaskServiceAdapterException_ShouldPropagateException() {
        // Arrange
        List<Task> taskList = Arrays.asList(testTask);
        List<ParamValueVehicleOperatorDTO> vehicleOperators = Arrays.asList(testVehicleOperator);


        when(taskParamService.getVehicleOperators(testTask)).thenReturn(vehicleOperators);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterDetailsDTO(testTask))
                .thenReturn(testTaskDTO);
        when(taskInstanceRegistrationRequestMapper.toTaskInstanceRegisterRequest(any()))
                .thenReturn(new TaskInstanceRegistrationRequest());
        when(taskServiceAdapter.registerTaskInstance(any(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Task service adapter error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () ->
                taskService.registerTaskInstances(taskList, testTransportOrder));

        verify(taskParamService).getVehicleOperators(testTask);
        verifyNoInteractions(vehicleOperatorService);
    }
}
