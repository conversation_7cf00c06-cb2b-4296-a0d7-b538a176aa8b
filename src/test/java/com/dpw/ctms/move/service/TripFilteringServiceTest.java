package com.dpw.ctms.move.service;

import com.dpw.ctms.move.constants.TripFieldConstants;
import com.dpw.ctms.move.entity.*;
import com.dpw.ctms.move.entity.Time;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.ShipmentStatus;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import com.dpw.ctms.move.enums.TripStatus;
import com.dpw.ctms.move.mapper.TripMappingService;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.TripListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.request.common.Sort;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import com.dpw.ctms.move.utils.Faker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
public class TripFilteringServiceTest {

    @Mock
    private TripRepository tripRepository;

    @Mock
    private TripMappingService tripMappingService;

    @InjectMocks
    private TripFilteringService tripFilteringService;

    @Captor
    private ArgumentCaptor<Pageable> pageableCaptor;

    private TripListingRequest tripListingRequest;
    private List<Trip> tripEntities;
    private List<TripListingResponse> tripResponses;

    @BeforeEach
    void setUp() {
        // Setup request using utility
        TripListingRequest.Filter filter = Faker.createTripIdsFilter("TRIP001", "TRIP002");
        filter.setTripStatuses(Arrays.asList(TripStatus.CREATED.name(), "ENROUTE"));

        tripListingRequest = Faker.createCustomTripListingRequest(
                Faker.DEFAULT_PAGINATION,
                Faker.CREATED_AT_DESC_SORT,
                filter
        );

        // Setup trip entities
        tripEntities = Arrays.asList(
                createTripEntity("TRIP001", TripStatus.CREATED),
                createTripEntity("TRIP002", TripStatus.IN_PROGRESS)
        );

        // Setup trip responses
        tripResponses = Arrays.asList(
                createTripResponse("TRIP001", TripStatus.CREATED),
                createTripResponse("TRIP002", TripStatus.IN_PROGRESS)
        );

        // Default mock setup for the optimized repository method
        // Individual tests can override this as needed
        Page<Trip> defaultTripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        lenient().when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class)))
                .thenReturn(defaultTripPage);

        // Setup default mapping responses
        lenient().when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        lenient().when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));
    }

    @Test
    void filterTrips_WithValidRequest_ShouldReturnFilteredTrips() {
        // Arrange - using default mocks from setUp()

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(tripListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());
        assertEquals("TRIP002", response.getData().get(1).getCode());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify pagination and sorting
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber());
        assertEquals(10, pageable.getPageSize());
        assertEquals("createdAt: DESC", pageable.getSort().toString());
    }

    @Test
    void filterTrips_WithNullFilter_ShouldReturnAllTrips() {
        // Arrange
        TripListingRequest requestWithNullFilter = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(null)
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNullFilter);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
    }

    @Test
    void filterTrips_WithNullPagination_ShouldUseDefaultPagination() {
        // Arrange
        TripListingRequest requestWithNullPagination = TripListingRequest.builder()
                .pagination(null)
                .sort(new Sort("createdAt", "DESC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNullPagination);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify default pagination
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber()); // Default page number
        assertEquals(20, pageable.getPageSize()); // Default page size
    }

    @Test
    void filterTrips_WithNullSort_ShouldUseDefaultSort() {
        // Arrange
        TripListingRequest requestWithNullSort = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(null)
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNullSort);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify default sort
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("createdAt: DESC", pageable.getSort().toString()); // Default sort
    }

    @Test
    void filterTrips_WithEmptyResult_ShouldReturnEmptyList() {
        // Arrange
        Page<Trip> emptyPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(tripListingRequest);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertEquals(0, response.getData().size());
    }

    @Test
    void filterTrips_WithAllFilterCriteria_ShouldApplyAllFilters() {
        // Arrange
        TripListingRequest.Filter complexFilter = Faker.createComplexTripFilter();
        TripListingRequest complexRequest = Faker.createTripListingRequestWithFilter(complexFilter);

        Trip complexTrip = createComplexTripEntity();
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(complexTrip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(complexTrip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(complexRequest);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());
    }

    @Test
    void filterTrips_WithInvalidStatus_ShouldIgnoreInvalidStatus() {
        // Arrange
        TripListingRequest.Filter filterWithInvalidStatus = new TripListingRequest.Filter();
        filterWithInvalidStatus.setTripStatuses(Arrays.asList(TripStatus.CREATED.name(), "INVALID_STATUS"));

        TripListingRequest requestWithInvalidStatus = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithInvalidStatus)
                .build();

        Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithInvalidStatus);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithAllInvalidStatuses_ShouldReturnNoResults() {
        // Arrange
        TripListingRequest.Filter filterWithAllInvalidStatuses = new TripListingRequest.Filter();
        filterWithAllInvalidStatuses.setTripStatuses(Arrays.asList("INVALID_STATUS_1", "INVALID_STATUS_2"));

        TripListingRequest requestWithAllInvalidStatuses = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithAllInvalidStatuses)
                .build();

        // Mock empty result for invalid statuses
        Page<Trip> emptyTripPage = new PageImpl<>(Collections.emptyList(), Pageable.ofSize(10), 0);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(emptyTripPage);

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithAllInvalidStatuses);

        // Assert
        assertNotNull(response);
        assertEquals(0, response.getTotalRecords());
        assertEquals(0, response.getData().size());
    }

    @Test
    void filterTrips_WithEmptyFilterFields_ShouldReturnAllTrips() {
        // Arrange
        TripListingRequest.Filter emptyFilter = new TripListingRequest.Filter();
        emptyFilter.setTripIds(Collections.emptyList());
        emptyFilter.setTripStatuses(Collections.emptyList());
        emptyFilter.setTransportOrderIds(Collections.emptyList());
        emptyFilter.setCustomerOrderIds(Collections.emptyList());
        emptyFilter.setVendorIds(Collections.emptyList());

        TripListingRequest requestWithEmptyFilter = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort(TripFieldConstants.SortFields.API_CREATED_AT, "DESC"))
                .filter(emptyFilter)
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithEmptyFilter);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
    }

    @Test
    void filterTrips_WithBasicSortFields_ShouldSortCorrectly() {
        // Test basic sorting fields
        String[] sortFields = {"code", "status", "createdAt", "updatedAt", "createdBy", "updatedBy"};

        for (String sortField : sortFields) {
            // Arrange
            TripListingRequest request = TripListingRequest.builder()
                    .pagination(new Pagination(0, 10))
                    .sort(new Sort(sortField, "ASC"))
                    .filter(new TripListingRequest.Filter())
                    .build();

            Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
            Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

            when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
            when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

            // Act
            ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

            // Assert
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertEquals(1, response.getTotalRecords(), "Should return 1 record for sort field: " + sortField);

            // Verify the correct Pageable was used
            verify(tripRepository).findAllOptimized(any(Specification.class), argThat((Pageable pageable) ->
                    pageable.getSort().getOrderFor(sortField) != null ||
                            pageable.getSort().getOrderFor(TripFieldConstants.CREATED_AT) != null // fallback for invalid fields
            ));

            reset(tripRepository, tripMappingService);
        }
    }

    @Test
    void filterTrips_WithLocationSortFields_ShouldSortCorrectly() {
        // Test location sorting fields
        String[] locationSortFields = {"originLocation", "destinationLocation"};

        for (String sortField : locationSortFields) {
            // Arrange
            TripListingRequest request = TripListingRequest.builder()
                    .pagination(new Pagination(0, 10))
                    .sort(new Sort(sortField, "DESC"))
                    .filter(new TripListingRequest.Filter())
                    .build();

            Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
            trip.setExternalOriginLocationCode("LOC001");
            trip.setExternalDestinationLocationCode("LOC002");

            Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

            when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
            when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

            // Act
            ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

            // Assert
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertEquals(1, response.getTotalRecords(), "Should return 1 record for sort field: " + sortField);

            reset(tripRepository, tripMappingService);
        }
    }

    @Test
    void filterTrips_WithTimestampSortFields_ShouldSortCorrectly() {
        // Test timestamp sorting fields
        String[] timestampSortFields = {"expectedStartAt", "expectedEndAt", "actualStartAt", "actualEndAt"};

        for (String sortField : timestampSortFields) {
            // Arrange
            TripListingRequest request = TripListingRequest.builder()
                    .pagination(new Pagination(0, 10))
                    .sort(new Sort(sortField, "ASC"))
                    .filter(new TripListingRequest.Filter())
                    .build();

            Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
            trip.setExpectedStartAt(new Time(1000L, "UTC"));
            trip.setExpectedEndAt(new Time(2000L, "UTC"));
            trip.setActualStartAt(new Time(1100L, "UTC"));
            trip.setActualEndAt(new Time(2100L, "UTC"));

            Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

            when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
            when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

            // Act
            ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

            // Assert
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertEquals(1, response.getTotalRecords(), "Should return 1 record for sort field: " + sortField);

            reset(tripRepository, tripMappingService);
        }
    }

    @Test
    void filterTrips_WithTransportOrderSortFields_ShouldSortCorrectly() {
        // Test transport order related sorting fields
        String[] transportOrderSortFields = {"transportOrderCode", "transportOrderStatus", "assignmentType", "vendorId"};

        for (String sortField : transportOrderSortFields) {
            // Arrange
            TripListingRequest request = TripListingRequest.builder()
                    .pagination(new Pagination(0, 10))
                    .sort(new Sort(sortField, "DESC"))
                    .filter(new TripListingRequest.Filter())
                    .build();

            Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
            // Transport order is already set in createTripEntity

            Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

            when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
            when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

            // Act
            ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

            // Assert
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertEquals(1, response.getTotalRecords(), "Should return 1 record for sort field: " + sortField);

            reset(tripRepository, tripMappingService);
        }
    }

    @Test
    void filterTrips_WithVehicleSortFields_ShouldSortCorrectly() {
        // Test vehicle related sorting fields
        String[] vehicleSortFields = {"vehicleRegistration", "vehicleType", "vehicleId"};

        for (String sortField : vehicleSortFields) {
            // Arrange
            TripListingRequest request = TripListingRequest.builder()
                    .pagination(new Pagination(0, 10))
                    .sort(new Sort(sortField, "ASC"))
                    .filter(new TripListingRequest.Filter())
                    .build();

            Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
            // Vehicle resource is already set in createTripEntity

            Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

            when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
            when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

            // Act
            ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

            // Assert
            assertNotNull(response, "Response should not be null for sort field: " + sortField);
            assertEquals(1, response.getTotalRecords(), "Should return 1 record for sort field: " + sortField);

            reset(tripRepository, tripMappingService);
        }
    }

    @Test
    void filterTrips_WithInvalidSortField_ShouldUseDefaultSort() {
        // Arrange
        TripListingRequest request = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("invalidSortField", "ASC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());

        // Verify that default sort field (createdAt) was used
        verify(tripRepository).findAllOptimized(any(Specification.class), argThat((Pageable pageable) ->
                pageable.getSort().getOrderFor(TripFieldConstants.CREATED_AT) != null
        ));
    }

    @Test
    void filterTrips_WithNegativePageNumber_ShouldUseDefaultPageNumber() {
        // Arrange
        TripListingRequest requestWithNegativePage = TripListingRequest.builder()
                .pagination(new Pagination(-1, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNegativePage);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify page number is set to 0
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(0, pageable.getPageNumber());
    }

    @Test
    void filterTrips_WithNegativePageSize_ShouldUseDefaultPageSize() {
        // Arrange
        TripListingRequest requestWithNegativeSize = TripListingRequest.builder()
                .pagination(new Pagination(0, -1))
                .sort(new Sort("createdAt", "DESC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNegativeSize);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify page size is set to default
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(20, pageable.getPageSize());
    }

    @Test
    void filterTrips_WithRepositoryError_ShouldThrowException() {
        // Arrange
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            tripFilteringService.filterTrips(tripListingRequest);
        });
    }

    @Test
    void filterTrips_WithMappingError_ShouldThrowException() {
        // Arrange
        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(any(Trip.class)))
                .thenThrow(new RuntimeException("Mapping error"));

        // Act & Assert
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            tripFilteringService.filterTrips(tripListingRequest);
        });
    }

    @Test
    void filterTrips_WithCustomSorting_ShouldApplyCustomSort() {
        // Arrange
        TripListingRequest requestWithCustomSort = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("code", "ASC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithCustomSort);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify custom sort
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("code: ASC", pageable.getSort().toString());
    }

    @Test
    void filterTrips_WithInvalidSortField_ShouldUseDefaultSortField() {
        // Arrange
        TripListingRequest requestWithInvalidSort = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("invalidField", "DESC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithInvalidSort);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify default sort field is used
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("createdAt: DESC", pageable.getSort().toString());
    }

    @Test
    void filterTrips_WithInvalidSortOrder_ShouldUseDefaultSortOrder() {
        // Arrange
        TripListingRequest requestWithInvalidSortOrder = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("code", "INVALID_ORDER"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithInvalidSortOrder);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify default sort order is used
        Pageable pageable = pageableCaptor.getValue();
        assertEquals("code: DESC", pageable.getSort().toString());
    }

    @Test
    void filterTrips_WithExcessivePageSize_ShouldLimitPageSize() {
        // Arrange
        TripListingRequest requestWithLargePageSize = TripListingRequest.builder()
                .pagination(new Pagination(0, 2000)) // Exceeds max page size
                .sort(new Sort("createdAt", "DESC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Page<Trip> tripPage = new PageImpl<>(tripEntities, Pageable.ofSize(10), 2);
        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(tripEntities.get(0))).thenReturn(tripResponses.get(0));
        when(tripMappingService.mapToResponse(tripEntities.get(1))).thenReturn(tripResponses.get(1));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithLargePageSize);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getTotalRecords());
        assertEquals(2, response.getData().size());
        verify(tripRepository).findAllOptimized(any(Specification.class), pageableCaptor.capture());

        // Verify page size is limited to max
        Pageable pageable = pageableCaptor.getValue();
        assertEquals(500, pageable.getPageSize()); // Max page size
    }

    @Test
    void filterTrips_WithConsignmentIds_ShouldFilterByConsignmentIds() {
        // Arrange
        TripListingRequest.Filter filterWithConsignmentIds = new TripListingRequest.Filter();
        filterWithConsignmentIds.setConsignmentIds(Arrays.asList("CONSIGNMENT001"));

        TripListingRequest requestWithConsignmentIds = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithConsignmentIds)
                .build();

        Trip trip = createComplexTripEntity();
        trip.getShipments().stream().toList().get(0).setExternalConsignmentId("CONSIGNMENT001");
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithConsignmentIds);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithTransportOrderStatuses_ShouldFilterByTransportOrderStatuses() {
        // Arrange
        TripListingRequest.Filter filterWithTransportOrderStatuses = new TripListingRequest.Filter();
        filterWithTransportOrderStatuses.setTransportOrderStatuses(Arrays.asList("ASSIGNED"));

        TripListingRequest requestWithTransportOrderStatuses = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithTransportOrderStatuses)
                .build();

        Trip trip = createComplexTripEntity();
        trip.getTransportOrder().setStatus(TransportOrderStatus.ASSIGNED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithTransportOrderStatuses);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithInvalidTransportOrderStatus_ShouldIgnoreInvalidStatus() {
        // Arrange
        TripListingRequest.Filter filterWithInvalidStatus = new TripListingRequest.Filter();
        filterWithInvalidStatus.setTransportOrderStatuses(Arrays.asList("ASSIGNED", "INVALID_STATUS"));

        TripListingRequest requestWithInvalidStatus = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithInvalidStatus)
                .build();

        Trip trip = createComplexTripEntity();
        trip.getTransportOrder().setStatus(TransportOrderStatus.ASSIGNED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithInvalidStatus);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithShipmentStatuses_ShouldFilterByShipmentStatuses() {
        // Arrange
        TripListingRequest.Filter filterWithShipmentStatuses = new TripListingRequest.Filter();
        filterWithShipmentStatuses.setShipmentStatuses(Arrays.asList("ALLOCATED"));

        TripListingRequest requestWithShipmentStatuses = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithShipmentStatuses)
                .build();

        Trip trip = createComplexTripEntity();
        trip.getShipments().stream().toList().get(0).setStatus(ShipmentStatus.ALLOCATED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithShipmentStatuses);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithInvalidShipmentStatus_ShouldIgnoreInvalidStatus() {
        // Arrange
        TripListingRequest.Filter filterWithInvalidStatus = new TripListingRequest.Filter();
        filterWithInvalidStatus.setShipmentStatuses(Arrays.asList("ALLOCATED", "INVALID_STATUS"));

        TripListingRequest requestWithInvalidStatus = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithInvalidStatus)
                .build();

        Trip trip = createComplexTripEntity();
        trip.getShipments().stream().toList().get(0).setStatus(ShipmentStatus.ALLOCATED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithInvalidStatus);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithExpectedPickupDateRange_ShouldFilterByExpectedPickupDateRange() {
        // Arrange
        TripListingRequest.Filter filterWithExpectedDates = Faker.createExpectedStartDateRangeFilter(1000L, 2000L);

        TripListingRequest requestWithExpectedDates = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithExpectedDates)
                .build();

        Trip trip = createComplexTripEntity();
        // Set trip-level timestamps instead of shipment-level
        trip.setExpectedStartAt(new Time(1500L, "UTC"));
        trip.setExpectedEndAt(new Time(1800L, "UTC"));
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithExpectedDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithActualPickupDateRange_ShouldFilterByActualPickupDateRange() {
        // Arrange
        TripListingRequest.Filter filterWithActualDates = Faker.createActualStartDateRangeFilter(1000L, 2000L);

        TripListingRequest requestWithActualDates = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithActualDates)
                .build();

        Trip trip = createComplexTripEntity();
        // Set trip-level timestamps instead of shipment-level
        trip.setActualStartAt(new Time(1500L, "UTC"));
        trip.setActualEndAt(new Time(1800L, "UTC"));
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithActualDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithExpectedDeliveryDateRange_ShouldFilterByExpectedDeliveryDateRange() {
        // Arrange
        TripListingRequest.Filter filterWithExpectedDeliveryDates = Faker.createExpectedEndDateRangeFilter(2000L, 3000L);

        TripListingRequest requestWithExpectedDeliveryDates = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithExpectedDeliveryDates)
                .build();

        Trip trip = createComplexTripEntity();
        trip.setExpectedEndAt(new Time(2500L, "UTC"));
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithExpectedDeliveryDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithActualDeliveryDateRange_ShouldFilterByActualDeliveryDateRange() {
        // Arrange
        TripListingRequest.Filter filterWithActualDeliveryDates = Faker.createActualEndDateRangeFilter(2000L, 3000L);

        TripListingRequest requestWithActualDeliveryDates = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithActualDeliveryDates)
                .build();

        Trip trip = createComplexTripEntity();
        trip.setActualEndAt(new Time(2500L, "UTC"));
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithActualDeliveryDates);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
    }

    @Test
    void filterTrips_WithPartialExpectedPickupDateRange_ShouldFilterCorrectly() {
        // Test with only from date
        TripListingRequest.Filter filterWithFromDate = Faker.createExpectedStartFromDateFilter(1000L);

        TripListingRequest requestWithFromDate = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithFromDate)
                .build();

        Trip trip = createComplexTripEntity();
        trip.setExpectedStartAt(new Time(1500L, "UTC"));
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithFromDate);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());

        // Test with only to date
        TripListingRequest.Filter filterWithToDate = Faker.createExpectedStartToDateFilter(2000L);

        TripListingRequest requestWithToDate = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithToDate)
                .build();

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);

        // Act
        ListResponse<TripListingResponse> responseToDate = tripFilteringService.filterTrips(requestWithToDate);

        // Assert
        assertNotNull(responseToDate);
        assertEquals(1, responseToDate.getTotalRecords());
        assertEquals(1, responseToDate.getData().size());
    }

    @Test
    void filterTrips_WithPodAttachedTrue_ShouldFilterTripsWithPodAttached() {
        // Arrange
        TripListingRequest.Filter filterWithPodAttached = new TripListingRequest.Filter();
        filterWithPodAttached.setIsPodAttached(true);

        TripListingRequest requestWithPodAttached = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithPodAttached)
                .build();

        Trip trip = createComplexTripEntity();
        // Set document attached to true for shipment
        trip.getShipments().stream().toList().get(0).setIsDocumentAttached(true);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithPodAttached);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());
    }

    @Test
    void filterTrips_WithPodAttachedFalse_ShouldFilterTripsWithoutPodAttached() {
        // Arrange
        TripListingRequest.Filter filterWithoutPodAttached = new TripListingRequest.Filter();
        filterWithoutPodAttached.setIsPodAttached(false);

        TripListingRequest requestWithoutPodAttached = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithoutPodAttached)
                .build();

        Trip trip = createComplexTripEntity();
        // Set document attached to false for shipment
        trip.getShipments().stream().toList().get(0).setIsDocumentAttached(false);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithoutPodAttached);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());
    }

    @Test
    void filterTrips_WithNullPodAttached_ShouldNotApplyPodFilter() {
        // Arrange
        TripListingRequest.Filter filterWithNullPod = new TripListingRequest.Filter();
        filterWithNullPod.setIsPodAttached(null);

        TripListingRequest requestWithNullPod = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("createdAt", "DESC"))
                .filter(filterWithNullPod)
                .build();

        Trip trip = createComplexTripEntity();
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip), Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class))).thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip)).thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(requestWithNullPod);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());
    }

    private Trip createTripEntity(String code, TripStatus status) {
        Trip trip = new Trip();
        trip.setCode(code);
        trip.setStatus(status);

        TransportOrder transportOrder = new TransportOrder();
        transportOrder.setCode("TO-" + code);
        transportOrder.setAssigneeIdentifier("VENDOR-" + code);
        trip.setTransportOrder(transportOrder);

        trip.setExternalOriginLocationCode("ORIGIN-" + code);
        trip.setExternalDestinationLocationCode("DEST-" + code);
        trip.getTransportOrder().setAssignmentType(AssignmentType.EXTERNAL);

        return trip;
    }

    private Trip createComplexTripEntity() {
        Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
        
        // Add vehicle resource
        VehicleResource vehicleResource = new VehicleResource();
        vehicleResource.setExternalResourceId("VEHICLE001");
        vehicleResource.setExternalVehicleTypeId("TRUCK");
        vehicleResource.setRegistrationNumber("REG001");
        vehicleResource.setTrip(trip);
        trip.setVehicleResource(vehicleResource);

        // Add trailer resources
        Set<TrailerResource> trailerResources = new HashSet<>();
        TrailerResource trailerResource = new TrailerResource();
        trailerResource.setExternalResourceId("TRAILER001");
        trailerResource.setTrip(trip);
        trailerResources.add(trailerResource);
        trip.setTrailerResources(trailerResources);

        // Add vehicle operator resources
        Set<VehicleOperatorResource> operatorResources = new HashSet<>();
        VehicleOperatorResource operatorResource = new VehicleOperatorResource();
        operatorResource.setExternalResourceId("DRIVER001");
        operatorResource.setTrip(trip);
        operatorResources.add(operatorResource);
        trip.setVehicleOperatorResources(operatorResources);

        // Add shipments
        Set<Shipment> shipments = new HashSet<>();
        Shipment shipment = new Shipment();
        shipment.setCode("SHIPMENT001");
        shipment.setExternalCustomerOrderId("CO001");
        shipment.setExternalConsignmentId("CONSIGNMENT001");
        shipment.setStatus(ShipmentStatus.ALLOCATED);
        shipment.setExpectedPickupAt(new Time(1500L, "UTC"));
        shipment.setExpectedDeliveryAt(new Time(1800L, "UTC"));
        shipment.setActualPickupAt(new Time(1600L, "UTC"));
        shipment.setActualDeliveryAt(new Time(1900L, "UTC"));
        shipment.setTrip(trip);
        shipments.add(shipment);
        trip.setShipments(shipments);

        return trip;
    }

    private TripListingResponse createTripResponse(String code, TripStatus status) {
        EnumLabelValueResponse statusInfo = new EnumLabelValueResponse(status.getDisplayName(), status.name());
        return TripListingResponse.builder()
                .code(code)
                .status(statusInfo)
                .transportOrderCode("TO-" + code)
                .build();
    }

    @Test
    void filterTrips_WithOptimizedApproach_ShouldUseOptimizedRepository() {
        // Arrange
        TripListingRequest request = TripListingRequest.builder()
                .pagination(new Pagination(0, 10))
                .sort(new Sort("code", "ASC"))
                .filter(new TripListingRequest.Filter())
                .build();

        Trip trip = createTripEntity("TRIP001", TripStatus.CREATED);
        Page<Trip> tripPage = new PageImpl<>(Collections.singletonList(trip),
                                           Pageable.ofSize(10), 1);

        when(tripRepository.findAllOptimized(any(Specification.class), any(Pageable.class)))
                .thenReturn(tripPage);
        when(tripMappingService.mapToResponse(trip))
                .thenReturn(createTripResponse("TRIP001", TripStatus.CREATED));

        // Act
        ListResponse<TripListingResponse> response = tripFilteringService.filterTrips(request);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getTotalRecords());
        assertEquals(1, response.getData().size());
        assertEquals("TRIP001", response.getData().get(0).getCode());

        // Verify that the optimized method was called instead of the regular findAll
        verify(tripRepository).findAllOptimized(any(Specification.class), any(Pageable.class));
        verify(tripRepository, never()).findAll(any(Specification.class), any(Pageable.class));
    }
}