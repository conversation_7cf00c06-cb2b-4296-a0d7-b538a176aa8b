package com.dpw.ctms.move.service.document.generator;

import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.enums.DocumentType;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.dto.document.BOLDocumentDataDTO;
import com.dpw.ctms.move.dto.document.DocumentGenerationContextDTO;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.integration.adapter.OmsServiceAdapter;
import com.dpw.ctms.move.integration.adapter.ResourceServiceAdapter;
import com.dpw.ctms.move.integration.response.oms.OmsListResponse;
import com.dpw.ctms.move.integration.response.oms.consignment.ConsignmentRecord;
import com.dpw.ctms.move.integration.response.resource.facility.FacilityRecord;
import com.dpw.ctms.move.integration.response.resource.uom.UomRecord;
import com.dpw.ctms.move.integration.response.ListResponse;
import com.dpw.ctms.move.service.ITripDataService;
import com.dpw.ctms.move.config.ConfigService;
import com.dpw.ctms.move.constants.ConfigConstants;
import com.dpw.ctms.move.constants.ProductPropertyConstants;
import com.dpw.ctms.move.util.JsonUtils;
import com.dpw.tmsutils.exception.GenericException;
import com.dpw.tmsutils.exception.NotFoundException;
import com.dpw.tmsutils.exception.TMSException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@DisplayName("BOL Document Generator Tests")
class BOLDocumentGeneratorTest extends BaseTest {

    @Autowired
    private BOLDocumentGenerator bolDocumentGenerator;

    @MockBean
    private ITripDataService tripDataService;

    @MockBean
    private OmsServiceAdapter omsServiceAdapter;

    @MockBean
    private ResourceServiceAdapter resourceServiceAdapter;

    @MockBean
    private ConfigService configService;

    @MockBean
    private com.dpw.ctms.move.util.DateTimeUtil dateTimeUtil;

    private Trip testTrip;
    private OmsListResponse<ConsignmentRecord> consignmentResponse;
    private ListResponse<FacilityRecord> facilityResponse;
    private ListResponse<UomRecord> uomResponse;

    @BeforeEach
    void setUp() {
        testTrip = Fakers.createBOLTestTrip();
        consignmentResponse = Fakers.createOmsConsignmentResponse();
        facilityResponse = Fakers.createFacilityResponse();
        uomResponse = Fakers.createUomResponse();
    }

    @Test
    @DisplayName("Should return correct document type")
    void shouldReturnCorrectDocumentType() {
        // When
        DocumentType result = bolDocumentGenerator.getDocumentType();

        // Then
        assertEquals(DocumentType.BOL, result);
    }

    @Test
    @DisplayName("Should generate context successfully with valid trip")
    void shouldGenerateContextSuccessfully() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        assertNotNull(result);
        assertEquals(tenant, result.getTenant());
        assertEquals(DocumentType.BOL, result.getDocumentType());
        assertNotNull(result.getDocumentData());

        BOLDocumentDataDTO documentData = result.getDocumentData();
        assertNotNull(documentData.getTripDetails());
        assertNotNull(documentData.getConsignmentDetailsMap());
        assertNotNull(documentData.getFacilityDetailsMap());
        assertFalse(documentData.getExternalConsignmentIds().isEmpty());
        assertFalse(documentData.getExternalFacilityCodes().isEmpty());
        
        // Verify timezone fields are populated
        assertNotNull(documentData.getPickUpTimeZoneId());
        assertNotNull(documentData.getDeliveryTimeZoneId());

        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsServiceAdapter).getConsignmentList(any(), any());
        verify(resourceServiceAdapter).getFacilityList(any(),any());
        verify(resourceServiceAdapter).getUomList(any(), any());
    }

    @Test
    @DisplayName("Should throw exception when trip not found")
    void shouldThrowExceptionWhenTripNotFound() {
        // Given
        String tripCode = "INVALID_TRIP";
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertTrue(exception.getCause() instanceof NotFoundException);
    }

    @Test
    @DisplayName("Should propagate OMS service exceptions")
    void shouldPropagateOmsServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException omsException = new GenericException("OMS Error", "OMS_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenThrow(omsException);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(omsException, exception.getCause());
    }

    @Test
    @DisplayName("Should propagate Resource service exceptions")
    void shouldPropagateResourceServiceExceptions() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        GenericException resourceException = new GenericException("Resource Error", "RES_001");

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenThrow(resourceException);
        when(resourceServiceAdapter.getUomList(any(),  any())).thenReturn(uomResponse);

        // When & Then
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);

        CompletionException exception = assertThrows(CompletionException.class, future::join);
        assertEquals(resourceException, exception.getCause());
    }

    @Test
    @DisplayName("Should handle empty consignment response")
    void shouldHandleEmptyConsignmentResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        OmsListResponse<ConsignmentRecord> emptyResponse = Fakers.createEmptyOmsResponse();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(emptyResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getConsignmentDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should handle empty facility response")
    void shouldHandleEmptyFacilityResponse() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        ListResponse<FacilityRecord> emptyFacilityResponse = Fakers.createEmptyFacilityResponse();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(emptyFacilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, Tenant.CFR);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertTrue(result.getDocumentData().getFacilityDetailsMap().isEmpty());
    }

    @Test
    @DisplayName("Should generate JSON successfully with valid JOLT configuration")
    void shouldGenerateJsonSuccessfully() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Mock all the dependencies for generateContext
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // Mock document configuration - simple transformation that copies input to output
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When
        Object result = bolDocumentGenerator.generateJson(tripCode, tenant);

        // Then
        assertNotNull(result);
        String jsonResult = JsonUtils.toJson(result);
        assertFalse(jsonResult.trim().isEmpty());
        assertTrue(jsonResult.contains("tripDetails") || jsonResult.contains("{}") || jsonResult.length() > 2);
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant);
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is null")
    void shouldThrowExceptionWhenJoltConfigIsNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(null);

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should throw TMSException when JOLT configuration is empty")
    void shouldThrowExceptionWhenJoltConfigIsEmpty() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);
        
        // Empty JOLT configuration array
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode emptyJoltConfig = objectMapper.createArrayNode();
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", emptyJoltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When & Then
        TMSException exception = assertThrows(TMSException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should propagate exception from generateContext")
    void shouldPropagateExceptionFromGenerateContext() {
        // Given
        String tripCode = "INVALID_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode))
                .thenThrow(new NotFoundException("Trip not found", "Trip with code " + tripCode + " not found"));

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
        
        assertTrue(exception.getMessage().contains("Trip not found"));
    }

    @Test
    @DisplayName("Should handle JOLT transformation returning null")
    void shouldHandleJoltTransformationReturningNull() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        // JOLT configuration that might return null (invalid transformation)
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "remove")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "")));
        
        JsonNode documentConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant))
                .thenReturn(documentConfig);

        // When
        Object result = bolDocumentGenerator.generateJson(tripCode, tenant);

        // Then - Should return empty object as JSON when transformation returns null
        assertNotNull(result);
        String jsonResult = JsonUtils.toJson(result);
        assertTrue(jsonResult.equals("{}") || jsonResult.equals("{ }") || jsonResult.trim().equals("{}"));
    }

    @Test
    @DisplayName("Should generate different JSON for different vendors")
    void shouldGenerateDifferentJsonForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant1 = Tenant.CFR;
        Tenant tenant2 = Tenant.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(),  any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),  any()))
                .thenReturn(uomResponse);

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Different JOLT configs for different vendors
        JsonNode joltConfig1 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "trip")));
        
        JsonNode joltConfig2 = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("tripDetails", "tripInfo")));
        
        JsonNode documentConfig1 = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig1);
        
        JsonNode documentConfig2 = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig2);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant1))
                .thenReturn(documentConfig1);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, tenant2))
                .thenReturn(documentConfig2);

        // When
        Object result1 = bolDocumentGenerator.generateJson(tripCode, tenant1);
        Object result2 = bolDocumentGenerator.generateJson(tripCode, tenant2);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        // Results should be different due to different JOLT transformations
        // (Though both might be valid JSON, the structure will differ)
        String jsonResult1 = JsonUtils.toJson(result1);
        String jsonResult2 = JsonUtils.toJson(result2);
        assertFalse(jsonResult1.trim().isEmpty());
        assertFalse(jsonResult2.trim().isEmpty());
        
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant1);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, tenant2);
    }

    @Test
    @DisplayName("Should retrieve correct templateId for different vendors")
    void shouldRetrieveCorrectTemplateIdForDifferentVendors() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant cfrTenant = Tenant.CFR;
        Tenant ihsTenant = Tenant.IHS;
        
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(), any()))
                .thenReturn(consignmentResponse);
        when(resourceServiceAdapter.getFacilityList(any(), any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(),any()))
                .thenReturn(uomResponse);

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode joltConfig = objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("operation", "shift")
                        .set("spec", objectMapper.createObjectNode()
                                .put("*", "&")));
        
        JsonNode cfrConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_CFR_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        JsonNode ihsConfig = objectMapper.createObjectNode()
                .put("templateId", "BOL_IHS_TEMPLATE_V1")
                .set("joltConfig", joltConfig);
        
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, cfrTenant))
                .thenReturn(cfrConfig);
        when(configService.getConfig(ConfigConstants.BOL_CONFIG, ihsTenant))
                .thenReturn(ihsConfig);

        // When
        bolDocumentGenerator.generateJson(tripCode, cfrTenant);
        bolDocumentGenerator.generateJson(tripCode, ihsTenant);

        // Then - Verify different template IDs are retrieved for different vendors
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, cfrTenant);
        verify(configService).getConfig(ConfigConstants.BOL_CONFIG, ihsTenant);
    }

    @Test
    @DisplayName("Should handle InterruptedException correctly")
    void shouldHandleInterruptedException() {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Mock to throw InterruptedException during execution
        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(),  any()))
                .thenAnswer(invocation -> {
                    Thread.currentThread().interrupt();
                    throw new InterruptedException("Thread was interrupted");
                });

        // When & Then
        GenericException exception = assertThrows(GenericException.class, 
                () -> bolDocumentGenerator.generateJson(tripCode, tenant));
    }

    @Test
    @DisplayName("Should calculate volume from dimensions when volume is not present")
    void shouldCalculateVolumeFromDimensions() throws ExecutionException, InterruptedException {
        // Given
        String tripCode = "BOL_TEST_TRIP";
        Tenant tenant = Tenant.CFR;
        
        // Create consignment response with dimensions but no volume
        OmsListResponse<ConsignmentRecord> consignmentWithDimensionsNoVolume = 
                Fakers.createOmsConsignmentResponseWithDimensionsNoVolume();

        when(tripDataService.getTripByCodeWithAllDetails(tripCode)).thenReturn(testTrip);
        when(omsServiceAdapter.getConsignmentList(any(), any()))
                .thenReturn(consignmentWithDimensionsNoVolume);
        when(resourceServiceAdapter.getFacilityList(any(), any()))
                .thenReturn(facilityResponse);
        when(resourceServiceAdapter.getUomList(any(), any()))
                .thenReturn(uomResponse);

        // When
        CompletableFuture<DocumentGenerationContextDTO<BOLDocumentDataDTO>> future =
                bolDocumentGenerator.generateContext(tripCode, tenant);
        DocumentGenerationContextDTO<BOLDocumentDataDTO> result = future.get();

        // Then
        assertNotNull(result);
        assertNotNull(result.getDocumentData());
        assertNotNull(result.getDocumentData().getConsignmentDetailsMap());
        
        // Verify volume was calculated
        var consignmentDetails = result.getDocumentData().getConsignmentDetailsMap().values().iterator().next();
        assertNotNull(consignmentDetails.getProductDetailsDTO());
        assertNotNull(consignmentDetails.getProductDetailsDTO().getProperties());
        
        var properties = consignmentDetails.getProductDetailsDTO().getProperties();
        
        // Check that VOLUME property exists using constant
        assertTrue(properties.containsKey(ProductPropertyConstants.VOLUME));
        
        // Verify the calculated volume (2.0 * 1.5 * 1.0 = 3.0)
        var volumeProperty = properties.get(ProductPropertyConstants.VOLUME);
        assertNotNull(volumeProperty);
        assertEquals(3.0, volumeProperty.getValue());
        
        // Verify volume has same UOM as LENGTH
        var lengthProperty = properties.get(ProductPropertyConstants.LENGTH);
        assertNotNull(lengthProperty);
        assertEquals(lengthProperty.getResourceUomId(), volumeProperty.getResourceUomId());
        
        // Verify other dimensions are still present using constants
        assertTrue(properties.containsKey(ProductPropertyConstants.LENGTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.BREADTH));
        assertTrue(properties.containsKey(ProductPropertyConstants.HEIGHT));
        
        verify(tripDataService).getTripByCodeWithAllDetails(tripCode);
        verify(omsServiceAdapter).getConsignmentList(any(), any());
    }
}