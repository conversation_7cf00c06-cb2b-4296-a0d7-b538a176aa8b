package com.dpw.ctms.move.servicebus.consumer;

import com.azure.core.util.BinaryData;
import com.azure.messaging.servicebus.ServiceBusProcessorClient;
import com.azure.messaging.servicebus.ServiceBusReceivedMessage;
import com.azure.messaging.servicebus.ServiceBusReceivedMessageContext;
import com.dpw.ctms.move.BaseTest;
import com.dpw.ctms.move.entity.Document;
import com.dpw.ctms.move.enums.DocumentOperationType;
import com.dpw.ctms.move.enums.DocumentStatus;
import com.dpw.ctms.move.enums.EntityType;
import com.dpw.ctms.move.enums.Tenant;
import com.dpw.ctms.move.fakers.Fakers;
import com.dpw.ctms.move.repository.DocumentRepository;
import com.dpw.ctms.move.request.documentEvent.FileMetadata;
import com.dpw.ctms.move.request.documentEvent.PreSignedUrlEvent;
import com.dpw.ctms.move.service.IDocumentsService;
import com.dpw.ctms.move.specification.DocumentSpecifications;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DocumentConsumerIntegrationTest extends BaseTest {
    @MockBean
    private ServiceBusReceivedMessageContext messageContext;

    @MockBean
    private ServiceBusReceivedMessage receivedMessage;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private IDocumentsService documentsService;

    @Autowired
    private DocumentConsumer documentConsumer;

    @BeforeEach
    public void setUp() {
        TenantContext.setCurrentTenant(Tenant.CFR.name());
        documentRepository.deleteAll();
    }

    @AfterEach
    void tearDown() {
        when(messageContext.getMessage()).thenReturn(receivedMessage);
        TenantContext.clear();
    }

    @Test
    public void testProcessMessage_createNewDocument_whenNoDocumentExists() throws Exception {
        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithFileMetadata(
            Fakers.createFileMetadata(Tenant.CFR)
        );

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        processMessageMethod.invoke(documentConsumer, receivedMessage);

        Optional<Document> savedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(event.getFileKey()));
        Document savedDocument = savedDocumentOptional.orElse(null);
        Assertions.assertNotNull(savedDocument);
        Assertions.assertEquals(DocumentStatus.INACTIVE, savedDocument.getStatus());
        Assertions.assertEquals(event.getFileKey(), savedDocument.getAsyncMappingUUID());
        Assertions.assertEquals(event.getFileName(), savedDocument.getFileName());
        Assertions.assertEquals(event.getPresignedDownloadUrl(), savedDocument.getPresignedDownloadUrl());
        Assertions.assertEquals(event.getFileSize(), savedDocument.getFileSize());
        Assertions.assertEquals(event.getFileType(), savedDocument.getFileType());
        Assertions.assertEquals(event.getClientIdentifier(), savedDocument.getClientIdentifier());
        Assertions.assertEquals(DocumentOperationType.UPLOAD, savedDocument.getDocumentOperationType());
        // Entity ID and Type should be null when created from PreSignedUrlEvent
        Assertions.assertNull(savedDocument.getEntityId());
        Assertions.assertNull(savedDocument.getEntityType());
    }

    @Test
    public void testProcessMessage_updateInactiveToActive_whenInactiveDocumentExists() throws Exception {
        // Given - an existing inactive document with entity information
        String asyncMappingUUID = "existing-file-" + UUID.randomUUID();
        Document existingDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.INACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .fileName("old-file.pdf")
                .fileSize(1000)
                .entityId("SHIP-001")
                .entityType(EntityType.SHIPMENT.name())
                .build();
        documentRepository.save(existingDoc);

        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithFileMetadata(
            Fakers.createFileMetadata(Tenant.CFR)
        );
        event.setFileKey(asyncMappingUUID);
        event.setFileName("updated-file.pdf");
        event.setFileSize(2000);

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing the message
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should be updated and status changed to ACTIVE
        Optional<Document> updatedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(asyncMappingUUID));
        Assertions.assertTrue(updatedDocumentOptional.isPresent());
        Document updatedDocument = updatedDocumentOptional.get();
        Assertions.assertEquals(DocumentStatus.ACTIVE, updatedDocument.getStatus());
        Assertions.assertEquals("updated-file.pdf", updatedDocument.getFileName());
        Assertions.assertEquals(2000, updatedDocument.getFileSize());
        Assertions.assertEquals(event.getFileKey(), updatedDocument.getAsyncMappingUUID());
        Assertions.assertEquals(event.getPresignedDownloadUrl(), updatedDocument.getPresignedDownloadUrl());
    }

    @Test
    public void testProcessMessage_noUpdate_whenActiveDocumentExists() throws Exception {
        // Given - an existing active document
        String asyncMappingUUID = "active-file-" + UUID.randomUUID();
        Document activeDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.ACTIVE)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .fileName("active-file.pdf")
                .fileSize(3000)
                .build();
        documentRepository.save(activeDoc);

        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithFileMetadata(
            Fakers.createFileMetadata(Tenant.CFR)
        );
        event.setFileKey(asyncMappingUUID);
        event.setFileName("new-file.pdf");
        event.setFileSize(4000);

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing the message
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should NOT be updated
        Optional<Document> unchangedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(asyncMappingUUID));
        Assertions.assertTrue(unchangedDocumentOptional.isPresent());
        Document unchangedDocument = unchangedDocumentOptional.get();
        Assertions.assertEquals(DocumentStatus.ACTIVE, unchangedDocument.getStatus());
        Assertions.assertEquals("active-file.pdf", unchangedDocument.getFileName()); // Should not change
        Assertions.assertEquals(3000, unchangedDocument.getFileSize()); // Should not change
        Assertions.assertEquals(asyncMappingUUID, unchangedDocument.getAsyncMappingUUID()); // Should not change
    }

    @Test
    public void testProcessMessage_noUpdate_whenDiscardedDocumentExists() throws Exception {
        // Given - an existing discarded document
        String asyncMappingUUID = "discarded-file-" + UUID.randomUUID();
        Document discardedDoc = Document.builder()
                .asyncMappingUUID(asyncMappingUUID)
                .status(DocumentStatus.DISCARDED)
                .documentOperationType(DocumentOperationType.UPLOAD)
                .fileName("discarded-file.pdf")
                .fileSize(5000)
                .build();
        documentRepository.save(discardedDoc);

        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithFileMetadata(
            Fakers.createFileMetadata(Tenant.CFR)
        );
        event.setFileKey(asyncMappingUUID);
        event.setFileName("new-file.pdf");
        event.setFileSize(6000);

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing the message
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should NOT be updated
        Optional<Document> unchangedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(asyncMappingUUID));
        Assertions.assertTrue(unchangedDocumentOptional.isPresent());
        Document unchangedDocument = unchangedDocumentOptional.get();
        Assertions.assertEquals(DocumentStatus.DISCARDED, unchangedDocument.getStatus());
        Assertions.assertEquals("discarded-file.pdf", unchangedDocument.getFileName()); // Should not change
        Assertions.assertEquals(5000, unchangedDocument.getFileSize()); // Should not change
    }

    @Test
    public void testProcessMessage_throwsException_whenFileMetadataIsNull() throws Exception {
        // Given - PreSignedUrlEvent without FileMetadata
        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithoutFileMetadata();

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When/Then - should throw exception
        Exception exception = Assertions.assertThrows(InvocationTargetException.class, () -> {
            processMessageMethod.invoke(documentConsumer, receivedMessage);
        });

        Assertions.assertTrue(exception.getCause() instanceof RuntimeException);
        Assertions.assertEquals("Failed to process message", exception.getCause().getMessage());

        // No document should be saved
        Optional<Document> documentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(event.getFileKey()));
        Assertions.assertFalse(documentOptional.isPresent());
    }

    @Test
    public void testProcessMessage_throwsException_whenTenantIsNull() throws Exception {
        // Given - FileMetadata with null tenant
        FileMetadata fileMetadata = new FileMetadata();
        fileMetadata.setTenant(null);

        PreSignedUrlEvent event = Fakers.createPreSignedUrlEventWithFileMetadata(fileMetadata);

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When/Then - should throw exception
        Exception exception = Assertions.assertThrows(InvocationTargetException.class, () -> {
            processMessageMethod.invoke(documentConsumer, receivedMessage);
        });

        Assertions.assertTrue(exception.getCause() instanceof RuntimeException);
    }

    @Test
    public void testProcessMessage_throwsException_whenInvalidJson() throws Exception {
        // Given - invalid JSON
        String invalidJson = "{ invalid json }";
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(invalidJson));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When/Then - should throw exception
        Exception exception = Assertions.assertThrows(InvocationTargetException.class, () -> {
            processMessageMethod.invoke(documentConsumer, receivedMessage);
        });

        Assertions.assertTrue(exception.getCause() instanceof RuntimeException);
        Assertions.assertEquals("Failed to process message", exception.getCause().getMessage());
    }

    @Test
    public void testProcessMessage_handlesAllFieldsPopulated() throws Exception {
        // Given - PreSignedUrlEvent with all fields populated
        PreSignedUrlEvent event = Fakers.createCompletePreSignedUrlEvent();

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing the message
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - all fields should be properly mapped
        Optional<Document> savedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(event.getFileKey()));
        Assertions.assertTrue(savedDocumentOptional.isPresent());
        Document savedDocument = savedDocumentOptional.get();
        Assertions.assertEquals(event.getFileKey(), savedDocument.getAsyncMappingUUID());
        Assertions.assertEquals(event.getFileName(), savedDocument.getFileName());
        Assertions.assertEquals(event.getPresignedDownloadUrl(), savedDocument.getPresignedDownloadUrl());
        Assertions.assertEquals(event.getFileSize(), savedDocument.getFileSize());
        Assertions.assertEquals(event.getFileType(), savedDocument.getFileType());
        Assertions.assertEquals(event.getClientIdentifier(), savedDocument.getClientIdentifier());
        Assertions.assertNotNull(savedDocument.getFileMetadata());
    }

    @Test
    public void testProcessMessage_handlesMinimalFields() throws Exception {
        // Given - PreSignedUrlEvent with minimal required fields
        FileMetadata fileMetadata = Fakers.createFileMetadata(Tenant.CFR);
        PreSignedUrlEvent event = new PreSignedUrlEvent();
        event.setFileKey("key-minimal-" + UUID.randomUUID());
        event.setFileIdentifier("file-minimal-" + UUID.randomUUID());
        event.setClientIdentifier("client-minimal-" + UUID.randomUUID());
        event.setFileMetadata(fileMetadata);

        String messageBody = objectMapper.writeValueAsString(event);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing the message
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should be created with available fields
        Optional<Document> savedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(event.getFileKey()));
        Assertions.assertTrue(savedDocumentOptional.isPresent());
        Document savedDocument = savedDocumentOptional.get();
        Assertions.assertEquals(event.getFileKey(), savedDocument.getAsyncMappingUUID());
        Assertions.assertEquals(event.getFileIdentifier(), savedDocument.getFileIdentifier());
        Assertions.assertEquals(event.getClientIdentifier(), savedDocument.getClientIdentifier());
        Assertions.assertEquals(DocumentStatus.INACTIVE, savedDocument.getStatus());
        Assertions.assertEquals(DocumentOperationType.UPLOAD, savedDocument.getDocumentOperationType());
    }

    @Test
    public void testProcessMessage_multipleUpdatesToSameDocument() throws Exception {
        // Given - a new document
        PreSignedUrlEvent initialEvent = Fakers.createPreSignedUrlEventWithFileMetadata(
            Fakers.createFileMetadata(Tenant.CFR)
        );
        
        String messageBody = objectMapper.writeValueAsString(initialEvent);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - first processing creates document
        processMessageMethod.invoke(documentConsumer, receivedMessage);

        Optional<Document> firstSaveOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(initialEvent.getFileKey()));
        Assertions.assertTrue(firstSaveOptional.isPresent());
        Document firstSave = firstSaveOptional.get();
        Assertions.assertEquals(DocumentStatus.INACTIVE, firstSave.getStatus());
        Assertions.assertEquals("test.pdf", firstSave.getFileName());

        // When - second processing with updated data AND entity information (simulating real scenario)
        initialEvent.setFileName("updated-document.pdf");
        initialEvent.setFileSize(4096);
        initialEvent.setPresignedDownloadUrl("https://example.com/download/updated");
        
        // Manually set entityId to simulate how it would be populated in real scenario
        Document existingDoc = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(initialEvent.getFileKey())).get();
        existingDoc.setEntityId("SHIP-001");
        existingDoc.setEntityType(EntityType.SHIPMENT.name());
        documentRepository.save(existingDoc);

        String updatedMessageBody = objectMapper.writeValueAsString(initialEvent);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(updatedMessageBody));

        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should be updated with new data and status ACTIVE
        Optional<Document> updatedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(initialEvent.getFileKey()));
        Assertions.assertTrue(updatedDocumentOptional.isPresent());
        Document updatedDocument = updatedDocumentOptional.get();
        Assertions.assertEquals(DocumentStatus.ACTIVE, updatedDocument.getStatus());
        Assertions.assertEquals("updated-document.pdf", updatedDocument.getFileName());
        Assertions.assertEquals(4096, updatedDocument.getFileSize());
        Assertions.assertEquals("https://example.com/download/updated", updatedDocument.getPresignedDownloadUrl());

        // When - third processing (document already active)
        initialEvent.setFileName("should-not-update.pdf");
        String thirdMessageBody = objectMapper.writeValueAsString(initialEvent);
        when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(thirdMessageBody));

        processMessageMethod.invoke(documentConsumer, receivedMessage);

        // Then - document should remain unchanged
        Optional<Document> unchangedDocumentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(initialEvent.getFileKey()));
        Assertions.assertTrue(unchangedDocumentOptional.isPresent());
        Document unchangedDocument = unchangedDocumentOptional.get();
        Assertions.assertEquals(DocumentStatus.ACTIVE, unchangedDocument.getStatus());
        Assertions.assertEquals("updated-document.pdf", unchangedDocument.getFileName()); // Should not change
    }

    @Test
    public void testProcessMessage_concurrentDocumentCreation() throws Exception {
        // Given - multiple events for different documents
        List<PreSignedUrlEvent> events = List.of(
            Fakers.createPreSignedUrlEventWithFileMetadata(Fakers.createFileMetadata(Tenant.CFR)),
            Fakers.createPreSignedUrlEventWithFileMetadata(Fakers.createFileMetadata(Tenant.CFR)),
            Fakers.createPreSignedUrlEventWithFileMetadata(Fakers.createFileMetadata(Tenant.CFR))
        );

        java.lang.reflect.Method processMessageMethod = DocumentConsumer.class.getDeclaredMethod("processMessage", ServiceBusReceivedMessage.class);
        processMessageMethod.setAccessible(true);

        // When - processing all messages
        for (PreSignedUrlEvent event : events) {
            String messageBody = objectMapper.writeValueAsString(event);
            when(receivedMessage.getBody()).thenReturn(BinaryData.fromString(messageBody));
            processMessageMethod.invoke(documentConsumer, receivedMessage);
        }

        // Then - all documents should be created
        for (PreSignedUrlEvent event : events) {
            Optional<Document> documentOptional = documentRepository.findOne(DocumentSpecifications.byAsyncMappingUUIDSpec(event.getFileKey()));
            Assertions.assertTrue(documentOptional.isPresent());
            Document document = documentOptional.get();
            Assertions.assertEquals(DocumentStatus.INACTIVE, document.getStatus());
            Assertions.assertEquals(event.getFileKey(), document.getAsyncMappingUUID());
        }

        // Verify total count
        List<Document> allDocuments = documentRepository.findAll();
        Assertions.assertEquals(3, allDocuments.size());
    }

    @Test
    void testPreDestroyHook() {
        // Given - a mock ServiceBusProcessorClient
        ServiceBusProcessorClient mockClient = mock(ServiceBusProcessorClient.class);
        ReflectionTestUtils.setField(documentConsumer, "serviceBusProcessorClient", mockClient);

        // When - shutdown is called
        documentConsumer.shutdown();

        // Then - client should be closed
        verify(mockClient).close();
    }
}